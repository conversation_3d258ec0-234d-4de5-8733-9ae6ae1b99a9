import { Request, Response } from "express";
import { prisma } from "../utils/prisma";
import {
  LibraryTemplateQuestionGroupSchema,
  updateLibraryTemplateQuestionGroupSchema,
} from "../validators/libraryTemplateQuestionGroupValidator";
import libraryTemplateQuestionGroupRepository from "../repositories/libraryTemplateQuestionGroupRepository";

export const createLibraryTemplateQuestionGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const result = LibraryTemplateQuestionGroupSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
    }
    const libraryData = result.data;

    const libraryGroup = await libraryTemplateQuestionGroupRepository.create(
      libraryData
    );

    return res.status(200).json({
      success: true,
      message: "library template group created",
      data: { libraryGroup },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error creating library template group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};
export const updateLibraryTemplateQuestionGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const result = updateLibraryTemplateQuestionGroupSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
    }

    const { id, title, order, parentGroupId, selectedQuestionIds } =
      result.data;

    const updates: any = {
      title,
      order,
      parentGroupId,
    };

    if (selectedQuestionIds && selectedQuestionIds.length > 0) {
      updates.libraryQuestions = {
        set: selectedQuestionIds.map((questionId: number) => ({
          id: questionId,
        })),
      };
    }

    const updateLibraryTemplateGroup =
      await libraryTemplateQuestionGroupRepository.update(id, updates);

    return res.status(200).json({
      success: true,
      message: "library template group updated successfully",
      data: { updateLibraryTemplateGroup },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error updating library template group ",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

export const deleteLibraryTemplateQuestionGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { id } = req.body;
    if (!id) {
      return res.status(404).json({
        success: false,
        message: "invalid id",
      });
    }
    await libraryTemplateQuestionGroupRepository.delete(id);

    return res.status(200).json({
      success: true,
      message: "group deleted sucess",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error delete library template group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const deleteLibraryTemplateQuestionAndGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { id } = req.body;
    if (!id) {
      return res.status(404).json({
        success: false,
        message: "invalid id",
      });
    }

    await libraryTemplateQuestionGroupRepository.deleteManyQuestionByGroup(id);

    await libraryTemplateQuestionGroupRepository.delete(id);

    return res.status(200).json({
      success: true,
      message: "group and question related to that group are delete succesfuly",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error delete library template group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const findAllLibraryTemplateGroupByLibrarytemplate = async (
  req: Request,
  res: Response
) => {
  try {
    const { id } = req.body;
    if (!id) {
      return res.status(404).json({
        sucess: false,
        message: "please provide project id",
      });
    }

    const projectGroup =
      await libraryTemplateQuestionGroupRepository.findAllByLibraryTemplate(id);

    return res.status(200).json({
      succes: true,
      message: "library template group fetched success",
      data: { projectGroup },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error getting library template group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const removeLibraryTemplateQuestionIdFromGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { groupId, questionId } = req.body;

    const group = await prisma.libraryTemplateQuestionGroup.findUnique({
      where: { id: Number(groupId) },
      include: { libraryQuestions: true },
    });

    if (!group) {
      return res.status(404).json({
        success: false,
        message: "library template group not found",
      });
    }

    const questionExists = group.libraryQuestions.some(
      (q) => q.id === Number(questionId)
    );
    if (!questionExists) {
      return res.status(404).json({
        success: false,
        message: "library question not found in this group",
      });
    }

    await prisma.libraryQuestion.update({
      where: { id: Number(questionId) },
      data: { libraryTemplateQuestionGroupId: null }, // 👈 remove its link to the group
    });

    res.status(200).json({
      success: true,
      message: "Question removed from library template group successfully",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error removing library template question from group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateLibraryTemplateQuestionFromOneGroupToAnother = async (
  req: Request,
  res: Response
) => {
  try {
    const { groupId, newGroupId, questionId } = req.body;
    if (!groupId || !newGroupId || !questionId) {
      return res.status(404).json({
        success: false,
        message: "id not found",
      });
    }

    const groupid = libraryTemplateQuestionGroupRepository.findById(groupId);
    if (!groupid) {
      return res.status(404).json({
        success: false,
        message: "group id not found",
      });
    }

    const newGroupid =
      libraryTemplateQuestionGroupRepository.findById(newGroupId);

    if (!newGroupid) {
      return res.status(404).json({
        success: false,
        message: "new group id not found",
      });
    }
    const question = await prisma.libraryQuestion.findUnique({
      where: { id: Number(questionId) },
    });

    if (!question) {
      return res.status(404).json({
        success: false,
        message: "library question id not found",
      });
    }

    if (question.libraryTemplateQuestionGroupId !== Number(groupId)) {
      return res.status(400).json({
        success: false,
        message: "library question does not belong to the old group",
      });
    }

    await prisma.libraryQuestion.update({
      where: { id: Number(questionId) },
      data: {
        libraryTemplateQuestionGroupId: Number(newGroupId),
      },
    });

    return res.status(200).json({
      success: true,
      message: "update success",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error adding library question from one group to another",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateOneLibraryTemplateGroupInsideAnotherGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { childGroupId, ParentGroupId } = req.body;

    const childGroupid =
      libraryTemplateQuestionGroupRepository.findById(childGroupId);
    if (!childGroupid) {
      return res.status(404).json({
        success: false,
        message: "group id not found",
      });
    }

    const ParentGroupid =
      libraryTemplateQuestionGroupRepository.findById(ParentGroupId);

    if (!ParentGroupid) {
      return res.status(404).json({
        success: false,
        message: "new group id not found",
      });
    }

    const update =
      await libraryTemplateQuestionGroupRepository.updateGroupInsideParentGroup(
        childGroupId,
        ParentGroupId
      );

    return res.status(200).json({
      success: false,
      message: "library question Group updated success",
      data: { update },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error moving library question group inside the parentGroup",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const removeLibraryTemplateGroupFromParentGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { groupId } = req.body;
    const groupid = libraryTemplateQuestionGroupRepository.findById(groupId);
    if (!groupId) {
      return res.status(400).json({
        success: false,
        message: "Group id is required",
      });
    }
    const group = await libraryTemplateQuestionGroupRepository.findById(
      groupId
    );

    if (!group) {
      return res.status(404).json({
        success: false,
        message: "Group id not found",
      });
    }

    // Optional: Check if group has a parentGroupId
    if (!group.parentGroupId) {
      return res.status(400).json({
        success: false,
        message: "library template group has no parent group to remove",
      });
    }

    await libraryTemplateQuestionGroupRepository.RemoveGroupFromParentGroup(
      groupId
    );

    return res.status(200).json({
      success: false,
      message: "library question remove success",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error adding library question from one group to another",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};
