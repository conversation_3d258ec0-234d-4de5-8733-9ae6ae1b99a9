{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_24e2925a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vxTVhA8XalJO92PbSKJ7lk2zRCVwDY/NQi5gp+1+9f4=", "__NEXT_PREVIEW_MODE_ID": "9dfbdfc4db13bb8c64c58b80f5eece18", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bfa1d200177c07cee7d6e9a417f4ffd95621b1aa2e515f8abbee690edaf701fb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7884da479e75075f36b6a14faf6db7cd597fe601e71f47be7dec1fe6b51b7b58"}}}, "sortedMiddleware": ["/"], "functions": {}}