import { InputType, Operator } from "@prisma/client";
import { z } from "zod";

export const questionOptionSchema = z.object({
  id: z.number().optional(),
  label: z.string().min(1, "Option label is required"),
  sublabel: z.string().nullable().optional(),
  code: z.string().min(1, "Option code is required"),
  nextQuestionId: z.number().optional().nullable(),
});

export const questionConditionSchema = z.object({
  id: z.number().optional(), // Optional for create; required for update
  operator: z.nativeEnum(Operator, {
    errorMap: () => ({ message: "Invalid operator value" }),
  }),
  value: z.string().min(1, "Condition value is required"),
  questionId: z.number().optional().nullable(), // May be null
  createdAt: z.date().optional(), // Usually set by DB
  updatedAt: z.date().optional(), // Usually set by DB
});

// Base schema without the superRefine validation
const baseQuestionSchema = z.object({
  label: z.string().min(1, "Question label is required"),
  inputType: z.nativeEnum(InputType, {
    errorMap: () => ({ message: "Invalid input type selected" }),
  }),
  isRequired: z.boolean().optional().default(false),
  hint: z.string().optional(),
  placeholder: z.string().optional(),
  position: z.number().optional(),
  questionOptions: z.array(questionOptionSchema).optional(),
  conditions: z.array(questionConditionSchema).optional().nullable(),
});

// Schema for validating questions with manual option entry
export const questionSchema = baseQuestionSchema.superRefine((data, ctx) => {
  if (
    (data.inputType === InputType.selectone ||
      data.inputType === InputType.selectmany) &&
    (!data.questionOptions || data.questionOptions.length === 0)
  ) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["questionOptions"],
      message: "Options are required for select input types.",
    });
  }
});

// Schema for validating questions with file upload
// This doesn't require questionOptions since they'll come from the file
export const questionWithFileSchema = baseQuestionSchema
  .omit({
    questionOptions: true,
  })
  .extend({
    // We don't need questionOptions for file uploads
    // but we'll keep the conditions
    conditions: z.array(questionConditionSchema).optional().nullable(),
  });

export const questionPositionsSchema = z.object({
  questionPositions: z.array(
    z.object({
      id: z.number(),
      position: z.number(),
    })
  ),
});
