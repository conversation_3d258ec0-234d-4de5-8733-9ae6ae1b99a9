{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/Modal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AnimatePresence, motion, easeInOut } from \"framer-motion\";\r\nimport { X } from \"lucide-react\";\r\nimport React from \"react\";\r\n\r\nconst Modal = ({\r\n  children,\r\n  className,\r\n  isOpen,\r\n  onClose,\r\n  preventOutsideClick = false,\r\n}: {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  preventOutsideClick?: boolean;\r\n}) => {\r\n  // Handle backdrop click with confirmation if needed\r\n  const handleBackdropClick = (e: React.MouseEvent) => {\r\n    if (preventOutsideClick) {\r\n      // Do nothing, prevent closing\r\n      return;\r\n    } else {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {isOpen && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          className=\"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40 p-4 overflow-y-auto\"\r\n          onClick={handleBackdropClick} // Handle backdrop click\r\n        >\r\n          <motion.div\r\n            initial={{ scale: 0.6, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            exit={{ scale: 0.6, opacity: 0 }}\r\n            transition={{ duration: 0.3, ease: easeInOut }}\r\n            className={`relative section flex flex-col max-w-4xl max-h-[90vh] overflow-y-auto ${className}`}\r\n            onClick={(e) => e.stopPropagation()} // Prevent clicks inside the modal from closing it\r\n          >\r\n            <X\r\n              onClick={onClose}\r\n              className=\"absolute self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300\"\r\n            />\r\n            {children}\r\n          </motion.div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n};\r\n\r\nexport default Modal;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAHA;;;;AAMA,MAAM,QAAQ,CAAC,EACb,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,sBAAsB,KAAK,EAO5B;IACC,oDAAoD;IACpD,MAAM,sBAAsB,CAAC;QAC3B,IAAI,qBAAqB;YACvB,8BAA8B;YAC9B;QACF,OAAO;YACL;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,UAAU;oBAAK,MAAM,oKAAA,CAAA,YAAS;gBAAC;gBAC7C,WAAW,CAAC,sEAAsE,EAAE,WAAW;gBAC/F,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAEjC,6LAAC,+LAAA,CAAA,IAAC;wBACA,SAAS;wBACT,WAAU;;;;;;oBAEX;;;;;;;;;;;;;;;;;AAMb;KAnDM;uCAqDS", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/axios.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nconst axiosInstance = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000/api\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n  withCredentials: true,\r\n});\r\n\r\n// Add request interceptor to handle auth token\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    // You can add auth token here if needed\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add response interceptor to handle errors\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.code === \"ERR_NETWORK\") {\r\n      console.error(\r\n        \"Network error - Please check if the backend server is running\"\r\n      );\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAGW;AAHX;;AAEA,MAAM,gBAAgB,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS,iEAAmC;IAC5C,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,+CAA+C;AAC/C,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,wCAAwC;IACxC,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,4CAA4C;AAC5C,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,IAAI,KAAK,eAAe;QAChC,QAAQ,KAAK,CACX;IAEJ;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/VerificationModal.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { AlertTriangle, Mail } from \"lucide-react\";\r\nimport axios from \"@/lib/axios\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\n\r\nconst VerificationModal = ({\r\n  email,\r\n  showModal,\r\n  setShowModal,\r\n}: {\r\n  email: string;\r\n  showModal: boolean;\r\n  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;\r\n}) => {\r\n  const dispatch = useDispatch();\r\n\r\n  const sendVerificationEmail = async () => {\r\n    try {\r\n      await axios.post(`/users/sendverificationemail`, {\r\n        email: email,\r\n      });\r\n    } catch (error) {\r\n      dispatch(\r\n        showNotification({\r\n          message:\r\n            \"Failed to send verification email. Please try again in a minute.\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    }\r\n  };\r\n  const [isResendDisabled, setIsResendDisabled] = useState<boolean>(true);\r\n  const [countDown, setCountDown] = useState<number>(60);\r\n\r\n  useEffect(() => {\r\n    let timer: number;\r\n    if (isResendDisabled && countDown > 0) {\r\n      timer = window.setInterval(() => {\r\n        setCountDown((prev) => prev - 1);\r\n      }, 1000);\r\n    } else if (countDown === 0) {\r\n      setIsResendDisabled(false);\r\n      setCountDown(60);\r\n    }\r\n    return () => clearInterval(timer);\r\n  }, [isResendDisabled, countDown]);\r\n\r\n  useEffect(() => {\r\n    if (showModal && email) {\r\n      sendVerificationEmail();\r\n    }\r\n    return () => {\r\n      setCountDown(60), setIsResendDisabled(true);\r\n    };\r\n  }, [showModal]);\r\n\r\n  const handleResend = async () => {\r\n    setIsResendDisabled(true);\r\n    const apiUrl =\r\n      process.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000/api\";\r\n    try {\r\n      await axios.post(`/users/sendverificationemail`, { email });\r\n    } catch (error) {\r\n      console.error(\"error sending email\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={() => setShowModal(false)}\r\n      className=\"flex flex-col items-center gap-4\"\r\n    >\r\n      <div className=\"rounded-full p-2 bg-primary-300\">\r\n        <Mail className=\"text-primary-500\" />\r\n      </div>\r\n      <h1 className=\"heading-text\">Check your email</h1>\r\n      <p className=\"flex flex-col items-center\">\r\n        We've sent a verification email to:{\" \"}\r\n        <span className=\"font-medium\">{email}</span>\r\n      </p>\r\n      <div className=\"flex gap-2 items-center bg-yellow-100 text-yellow-900 px-4 py-2 rounded-md\">\r\n        <AlertTriangle size={16} /> Didn't receive the email? Check your spam\r\n        folder\r\n      </div>\r\n      <button\r\n        className=\"btn-primary\"\r\n        onClick={handleResend}\r\n        disabled={isResendDisabled}\r\n      >\r\n        {isResendDisabled ? (\r\n          <div className=\"flex items-center gap-2\">\r\n            <span>Resend in {countDown}s</span>\r\n            <div className=\"size-4 animate-spin border-x-2 rounded-full\"></div>\r\n          </div>\r\n        ) : (\r\n          <span>Resend</span>\r\n        )}\r\n      </button>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { VerificationModal };\r\n"], "names": [], "mappings": ";;;AA8DM;;AA7DN;AACA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;;AAQA,MAAM,oBAAoB,CAAC,EACzB,KAAK,EACL,SAAS,EACT,YAAY,EAKb;;IACC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;gBAC/C,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SACE;gBACF,MAAM;YACR;QAEJ;IACF;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI;YACJ,IAAI,oBAAoB,YAAY,GAAG;gBACrC,QAAQ,OAAO,WAAW;mDAAC;wBACzB;2DAAa,CAAC,OAAS,OAAO;;oBAChC;kDAAG;YACL,OAAO,IAAI,cAAc,GAAG;gBAC1B,oBAAoB;gBACpB,aAAa;YACf;YACA;+CAAO,IAAM,cAAc;;QAC7B;sCAAG;QAAC;QAAkB;KAAU;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,aAAa,OAAO;gBACtB;YACF;YACA;+CAAO;oBACL,aAAa,KAAK,oBAAoB;gBACxC;;QACF;sCAAG;QAAC;KAAU;IAEd,MAAM,eAAe;QACnB,oBAAoB;QACpB,MAAM,SACJ,iEAAmC;QACrC,IAAI;YACF,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;gBAAE;YAAM;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS,IAAM,aAAa;QAC5B,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,6LAAC;gBAAG,WAAU;0BAAe;;;;;;0BAC7B,6LAAC;gBAAE,WAAU;;oBAA6B;oBACJ;kCACpC,6LAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAEjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2NAAA,CAAA,gBAAa;wBAAC,MAAM;;;;;;oBAAM;;;;;;;0BAG7B,6LAAC;gBACC,WAAU;gBACV,SAAS;gBACT,UAAU;0BAET,iCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;gCAAK;gCAAW;gCAAU;;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;;;;;;;;;;;yCAGjB,6LAAC;8BAAK;;;;;;;;;;;;;;;;;AAKhB;GAhGM;;QASa,4JAAA,CAAA,cAAW;;;KATxB", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/hooks/useAuth.tsx"], "sourcesContent": ["import {\r\n  setAuthenticatedUser,\r\n  setAuthError,\r\n  setAuthLoading,\r\n  setUnauthenticated,\r\n} from \"@/redux/slices/authSlice\";\r\nimport { RootState } from \"@/redux/store\";\r\nimport { UserSession } from \"@/types/authTypes\";\r\nimport { AxiosError, isAxiosError } from \"axios\";\r\nimport axios from \"@/lib/axios\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\nconst useAuth = (options?: { skipFetchUser?: boolean }) => {\r\n  const dispatch = useDispatch();\r\n  const router = useRouter();\r\n  const { status, user, error } = useSelector((state: RootState) => state.auth);\r\n\r\n  const fetchUserData = async () => {\r\n    try {\r\n      dispatch(setAuthLoading());\r\n      const response = await axios.get(`/users/me`);\r\n      const userData: UserSession = response.data;\r\n      dispatch(setAuthenticatedUser(userData));\r\n    } catch (error) {\r\n      // Handle errors, especially 401 Unauthorized\r\n      dispatch(setUnauthenticated());\r\n\r\n      if (isAxiosError(error)) {\r\n        console.error(\r\n          \"Auth error:\",\r\n          error.response?.status,\r\n          error.response?.data\r\n        );\r\n\r\n        // If error is 401 Unauthorized (including expired token)\r\n        if (error.response?.status === 401) {\r\n          router.push(\"/\");\r\n        } else {\r\n          // For other errors\r\n          dispatch(\r\n            setAuthError(error.response?.data?.message || error.message)\r\n          );\r\n        }\r\n      } else {\r\n        dispatch(\r\n          setAuthError(\r\n            error instanceof Error\r\n              ? error.message\r\n              : \"An unknown error occurred.\"\r\n          )\r\n        );\r\n      }\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!options?.skipFetchUser) {\r\n      fetchUserData();\r\n    }\r\n  }, [options?.skipFetchUser]);\r\n\r\n  // Add event listener for storage changes to handle logout across tabs\r\n  useEffect(() => {\r\n    const handleStorageChange = (e: StorageEvent) => {\r\n      if (e.key === \"logout\" && e.newValue === \"true\") {\r\n        dispatch(setUnauthenticated());\r\n        router.push(\"/\");\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"storage\", handleStorageChange);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"storage\", handleStorageChange);\r\n    };\r\n  }, [dispatch, router]);\r\n\r\n  const refreshAuthState = () => {\r\n    fetchUserData();\r\n  };\r\n\r\n  const signin = async (\r\n    data: { email: string; password: string },\r\n    onSuccess?: () => void,\r\n    onError?: (errorType?: string) => void\r\n  ) => {\r\n    try {\r\n      await axios.post(`/users/login`, data);\r\n      await fetchUserData();\r\n      onSuccess?.();\r\n    } catch (error) {\r\n      if (error instanceof AxiosError) {\r\n        const errorType = error.response?.data?.errorType;\r\n        onError?.(errorType);\r\n      } else {\r\n        onError?.();\r\n      }\r\n    }\r\n  };\r\n\r\n  const logout = async () => {\r\n    try {\r\n      await axios.post(`/users/logout`);\r\n      // Notify other tabs about logout\r\n      localStorage.setItem(\"logout\", \"true\");\r\n      // Remove the flag immediately to ensure future logout events still trigger\r\n      setTimeout(() => localStorage.removeItem(\"logout\"), 100);\r\n    } finally {\r\n      dispatch(setUnauthenticated());\r\n      router.push(\"/\");\r\n    }\r\n  };\r\n\r\n  return {\r\n    status,\r\n    user,\r\n    error,\r\n    isAuthenticated: status === \"authenticated\",\r\n    isLoading: status === \"loading\",\r\n    refreshAuthState,\r\n    signin,\r\n    logout,\r\n  };\r\n};\r\n\r\nexport { useAuth };\r\n"], "names": [], "mappings": ";;;AAAA;AAQA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,UAAU,CAAC;;IACf,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;+BAAE,CAAC,QAAqB,MAAM,IAAI;;IAE5E,MAAM,gBAAgB;QACpB,IAAI;YACF,SAAS,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD;YACtB,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;YAC5C,MAAM,WAAwB,SAAS,IAAI;YAC3C,SAAS,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,6CAA6C;YAC7C,SAAS,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD;YAE1B,IAAI,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;gBACvB,QAAQ,KAAK,CACX,eACA,MAAM,QAAQ,EAAE,QAChB,MAAM,QAAQ,EAAE;gBAGlB,yDAAyD;gBACzD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;oBAClC,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,mBAAmB;oBACnB,SACE,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO;gBAE/D;YACF,OAAO;gBACL,SACE,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EACT,iBAAiB,QACb,MAAM,OAAO,GACb;YAGV;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,CAAC,SAAS,eAAe;gBAC3B;YACF;QACF;4BAAG;QAAC,SAAS;KAAc;IAE3B,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;yDAAsB,CAAC;oBAC3B,IAAI,EAAE,GAAG,KAAK,YAAY,EAAE,QAAQ,KAAK,QAAQ;wBAC/C,SAAS,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD;wBAC1B,OAAO,IAAI,CAAC;oBACd;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC;qCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;4BAAG;QAAC;QAAU;KAAO;IAErB,MAAM,mBAAmB;QACvB;IACF;IAEA,MAAM,SAAS,OACb,MACA,WACA;QAEA,IAAI;YACF,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,EAAE;YACjC,MAAM;YACN;QACF,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,iJAAA,CAAA,aAAU,EAAE;gBAC/B,MAAM,YAAY,MAAM,QAAQ,EAAE,MAAM;gBACxC,UAAU;YACZ,OAAO;gBACL;YACF;QACF;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;YAChC,iCAAiC;YACjC,aAAa,OAAO,CAAC,UAAU;YAC/B,2EAA2E;YAC3E,WAAW,IAAM,aAAa,UAAU,CAAC,WAAW;QACtD,SAAU;YACR,SAAS,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD;YAC1B,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA,iBAAiB,WAAW;QAC5B,WAAW,WAAW;QACtB;QACA;QACA;IACF;AACF;GA/GM;;QACa,4JAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACQ,4JAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%28auth%29/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { VerificationModal } from \"@/components/modals/VerificationModal\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { <PERSON><PERSON>heck, Eye, EyeOff } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport React, { useState } from \"react\";\r\nimport { FieldValues, useForm } from \"react-hook-form\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { z } from \"zod\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\n\r\nconst signInSchema = z.object({\r\n  email: z\r\n    .string()\r\n    .min(1, \"Email is required\")\r\n    .email(\"Please enter a valid email address\"),\r\n  password: z.string().min(1, \"Password is required\"),\r\n});\r\n\r\ntype SignInFormValues = z.infer<typeof signInSchema>;\r\n\r\nconst page = () => {\r\n  const {\r\n    register,\r\n    formState: { errors, isSubmitting },\r\n    handleSubmit,\r\n    getValues,\r\n    watch,\r\n  } = useForm<SignInFormValues>({ resolver: zodResolver(signInSchema) });\r\n\r\n  // Watch password field to determine when to show the eye button\r\n  const passwordValue = watch(\"password\");\r\n\r\n  const router = useRouter();\r\n  const dispatch = useDispatch();\r\n\r\n  const [showVerificationModal, setShowVerificationModal] =\r\n    useState<boolean>(false);\r\n  const [showPassword, setShowPassword] = useState<boolean>(false);\r\n\r\n  const { signin } = useAuth({ skipFetchUser: true });\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    signin(\r\n      { email: data.email, password: data.password },\r\n      () => {\r\n        dispatch(\r\n          showNotification({ message: \"Sign in successful.\", type: \"success\" })\r\n        );\r\n        router.push(\"/dashboard\");\r\n      },\r\n      (errorType) => {\r\n        if (errorType === \"unverified\") {\r\n          setShowVerificationModal(true);\r\n        } else {\r\n          dispatch(\r\n            showNotification({\r\n              message: \"Invalid email or password. Please try again.\",\r\n              type: \"error\",\r\n            })\r\n          );\r\n        }\r\n      }\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center\">\r\n      <VerificationModal\r\n        email={getValues(\"email\")}\r\n        showModal={showVerificationModal}\r\n        setShowModal={setShowVerificationModal}\r\n      />\r\n      <div className=\"flex flex-col section w-11/12 mobile:w-4/5 tablet:w-lg\">\r\n        <div className=\"flex flex-col items-center gap-2 mb-8\">\r\n          <ShieldCheck size={36} />\r\n          <h1 className=\"text-2xl tablet:text-3xl font-semibold text-center\">\r\n            Sign in to your account\r\n          </h1>\r\n          <p className=\"text-neutral-700 text-center\">\r\n            Get started with data analysis tool\r\n          </p>\r\n        </div>\r\n        <form\r\n          className=\"flex flex-col gap-4 mb-4\"\r\n          onSubmit={handleSubmit(onSubmit)}\r\n        >\r\n          <div className=\"group label-input-group\">\r\n            <label htmlFor=\"email\" className=\"label-text\">\r\n              Email\r\n            </label>\r\n            <input\r\n              {...register(\"email\")}\r\n              id=\"email\"\r\n              type=\"email\"\r\n              placeholder=\"Enter your email address\"\r\n              className=\"input-field\"\r\n            />\r\n            {errors.email && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.email.message}`}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"group label-input-group\">\r\n            <label htmlFor=\"password\" className=\"label-text\">\r\n              Password\r\n            </label>\r\n            <div className=\"relative\">\r\n              <input\r\n                {...register(\"password\")}\r\n                id=\"password\"\r\n                type={showPassword ? \"text\" : \"password\"}\r\n                placeholder=\"Enter your password\"\r\n                className=\"input-field w-full pr-10\"\r\n              />\r\n              {passwordValue && passwordValue.length > 0 && (\r\n                <button\r\n                  type=\"button\"\r\n                  tabIndex={-1}\r\n                  className=\"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500\"\r\n                  onClick={() => setShowPassword(!showPassword)}\r\n                >\r\n                  {showPassword ? (\r\n                    <EyeOff className=\"h-4 w-4\" />\r\n                  ) : (\r\n                    <Eye className=\"h-4 w-4\" />\r\n                  )}\r\n                  <span className=\"sr-only\">\r\n                    {showPassword ? \"Hide\" : \"Show\"} password\r\n                  </span>\r\n                </button>\r\n              )}\r\n            </div>\r\n            {errors.password && (\r\n              <p className=\"text-red-500 text-sm\">{`${errors.password.message}`}</p>\r\n            )}\r\n          </div>\r\n          <button type=\"submit\" className=\"btn-primary\" disabled={isSubmitting}>\r\n            {isSubmitting ? (\r\n              <span className=\"flex items-center gap-2\">\r\n                Signing in{\" \"}\r\n                <div className=\"size-4 rounded-full border-x-2 animate-spin\"></div>\r\n              </span>\r\n            ) : (\r\n              \"Sign in\"\r\n            )}\r\n          </button>\r\n        </form>\r\n        <Link\r\n          href={\"/reset-password\"}\r\n          className=\"self-end underline text-neutral-700\"\r\n        >\r\n          forgot password?\r\n        </Link>\r\n\r\n        <div className=\"text-neutral-700 flex items-center gap-2\">\r\n          <span>Don't have an account?</span>\r\n          <Link\r\n            href=\"/signup\"\r\n            className=\"font-medium hover:text-neutral-900 duration-300\"\r\n          >\r\n            Sign up\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default page;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAcA,MAAM,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,uIAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,qBACP,KAAK,CAAC;IACT,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIA,MAAM,OAAO;;IACX,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,YAAY,EACZ,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QAAE,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IAAc;IAEpE,gEAAgE;IAChE,MAAM,gBAAgB,MAAM;IAE5B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,CAAC,uBAAuB,yBAAyB,GACrD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAE1D,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD,EAAE;QAAE,eAAe;IAAK;IAEjD,MAAM,WAAW,OAAO;QACtB,OACE;YAAE,OAAO,KAAK,KAAK;YAAE,UAAU,KAAK,QAAQ;QAAC,GAC7C;YACE,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBAAE,SAAS;gBAAuB,MAAM;YAAU;YAErE,OAAO,IAAI,CAAC;QACd,GACA,CAAC;YACC,IAAI,cAAc,cAAc;gBAC9B,yBAAyB;YAC3B,OAAO;gBACL,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;QACF;IAEJ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,OAAO,UAAU;gBACjB,WAAW;gBACX,cAAc;;;;;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,MAAM;;;;;;0CACnB,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,6LAAC;gCAAE,WAAU;0CAA+B;;;;;;;;;;;;kCAI9C,6LAAC;wBACC,WAAU;wBACV,UAAU,aAAa;;0CAEvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAQ,WAAU;kDAAa;;;;;;kDAG9C,6LAAC;wCACE,GAAG,SAAS,QAAQ;wCACrB,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;oCAEX,OAAO,KAAK,kBACX,6LAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,KAAK,CAAC,OAAO,EAAE;;;;;;;;;;;;0CAGlE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAAa;;;;;;kDAGjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACE,GAAG,SAAS,WAAW;gDACxB,IAAG;gDACH,MAAM,eAAe,SAAS;gDAC9B,aAAY;gDACZ,WAAU;;;;;;4CAEX,iBAAiB,cAAc,MAAM,GAAG,mBACvC,6LAAC;gDACC,MAAK;gDACL,UAAU,CAAC;gDACX,WAAU;gDACV,SAAS,IAAM,gBAAgB,CAAC;;oDAE/B,6BACC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEAEjB,6LAAC;wDAAK,WAAU;;4DACb,eAAe,SAAS;4DAAO;;;;;;;;;;;;;;;;;;;oCAKvC,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDAAwB,GAAG,OAAO,QAAQ,CAAC,OAAO,EAAE;;;;;;;;;;;;0CAGrE,6LAAC;gCAAO,MAAK;gCAAS,WAAU;gCAAc,UAAU;0CACrD,6BACC,6LAAC;oCAAK,WAAU;;wCAA0B;wCAC7B;sDACX,6LAAC;4CAAI,WAAU;;;;;;;;;;;2CAGjB;;;;;;;;;;;;kCAIN,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM;wBACN,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAjJM;;QAOA,iKAAA,CAAA,UAAO;QAKI,qIAAA,CAAA,YAAS;QACP,4JAAA,CAAA,cAAW;QAMT,oHAAA,CAAA,UAAO;;;uCAgIb", "debugId": null}}]}