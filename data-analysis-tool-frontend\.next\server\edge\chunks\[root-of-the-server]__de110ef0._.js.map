{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["// middleware.ts\r\nimport { NextRequest, NextResponse } from \"next/server\";\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  // Getting token from cookies\r\n  const token = request.cookies.get(\"token\")?.value;\r\n  const pathname = request.nextUrl.pathname;\r\n  \r\n  // Define auth pages (public pages)\r\n  const authPages = [\r\n    \"/\",\r\n    \"/signup\",\r\n    \"/reset-password\",\r\n    \"/reset-password/change-password\",\r\n  ];\r\n  \r\n  const isAuthPage = authPages.includes(pathname);\r\n\r\n  // Check if token is valid (exists and not expired)\r\n  let isValidToken = false;\r\n  \r\n  if (token) {\r\n    try {\r\n      // Try to validate the token without making an actual API call\r\n      // This is a simple check - you might want a more sophisticated validation\r\n      const tokenData = JSON.parse(atob(token.split('.')[1]));\r\n      const expiry = tokenData.exp * 1000; // Convert to milliseconds\r\n      isValidToken = Date.now() < expiry;\r\n    } catch (error) {\r\n      // If token parsing fails, consider it invalid\r\n      isValidToken = false;\r\n    }\r\n  }\r\n\r\n  // Special case for form-test routes\r\n  if (pathname.startsWith(\"/form-test\")) {\r\n    // Allow access to sign-in pages regardless of auth status\r\n    if (pathname.includes(\"/sign-in\")) {\r\n      return NextResponse.next();\r\n    }\r\n\r\n    // For main form-test pages, require authentication\r\n    if (!isValidToken) {\r\n      const url = request.nextUrl.clone();\r\n      url.pathname = pathname + \"/sign-in\";\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    return NextResponse.next();\r\n  }\r\n\r\n  // ✅ Authenticated users should not access auth pages\r\n  if (isValidToken && isAuthPage) {\r\n    const url = request.nextUrl.clone();\r\n    url.pathname = \"/dashboard\";\r\n    return NextResponse.redirect(url);\r\n  }\r\n  \r\n  // ✅ Unauthenticated users should not access protected pages\r\n  if (!isValidToken && !isAuthPage) {\r\n    const url = request.nextUrl.clone();\r\n    url.pathname = \"/\";\r\n    return NextResponse.redirect(url);\r\n  }\r\n  \r\n  // ✅ All good, continue as normal\r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\"/((?!_next/static|_next/image|favicon.ico|images|fonts).*)\"],\r\n};\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAChB;AAAA;;AAEO,eAAe,WAAW,OAAoB;IACnD,6BAA6B;IAC7B,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,UAAU;IAC5C,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,mCAAmC;IACnC,MAAM,YAAY;QAChB;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa,UAAU,QAAQ,CAAC;IAEtC,mDAAmD;IACnD,IAAI,eAAe;IAEnB,IAAI,OAAO;QACT,IAAI;YACF,8DAA8D;YAC9D,0EAA0E;YAC1E,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;YACrD,MAAM,SAAS,UAAU,GAAG,GAAG,MAAM,0BAA0B;YAC/D,eAAe,KAAK,GAAG,KAAK;QAC9B,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,eAAe;QACjB;IACF;IAEA,oCAAoC;IACpC,IAAI,SAAS,UAAU,CAAC,eAAe;QACrC,0DAA0D;QAC1D,IAAI,SAAS,QAAQ,CAAC,aAAa;YACjC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B;QAEA,mDAAmD;QACnD,IAAI,CAAC,cAAc;YACjB,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG,WAAW;YAC1B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,qDAAqD;IACrD,IAAI,gBAAgB,YAAY;QAC9B,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;QACjC,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,4DAA4D;IAC5D,IAAI,CAAC,gBAAgB,CAAC,YAAY;QAChC,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;QACjC,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,iCAAiC;IACjC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;KAA6D;AACzE"}}]}