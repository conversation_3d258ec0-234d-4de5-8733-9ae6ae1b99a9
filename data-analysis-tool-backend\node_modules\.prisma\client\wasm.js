
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  country: 'country',
  city: 'city',
  bio: 'bio',
  sector: 'sector',
  organizationType: 'organizationType',
  isVerified: 'isVerified',
  emailVerificationToken: 'emailVerificationToken',
  emailVerificationExpires: 'emailVerificationExpires',
  resetPasswordToken: 'resetPasswordToken',
  resetPasswordExpires: 'resetPasswordExpires',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  sector: 'sector',
  status: 'status',
  country: 'country',
  lastSubmissionAt: 'lastSubmissionAt',
  lastDeployedAt: 'lastDeployedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.QuestionScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  label: 'label',
  inputType: 'inputType',
  hint: 'hint',
  placeholder: 'placeholder',
  isRequired: 'isRequired',
  position: 'position',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  questionGroupId: 'questionGroupId'
};

exports.Prisma.QuestionOptionScalarFieldEnum = {
  id: 'id',
  label: 'label',
  sublabel: 'sublabel',
  code: 'code',
  questionId: 'questionId',
  nextQuestionId: 'nextQuestionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuestionConditionScalarFieldEnum = {
  id: 'id',
  operator: 'operator',
  value: 'value',
  questionId: 'questionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuestionGroupScalarFieldEnum = {
  id: 'id',
  title: 'title',
  order: 'order',
  parentGroupId: 'parentGroupId',
  projectId: 'projectId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  id: 'id',
  deviceInfo: 'deviceInfo',
  ipAddress: 'ipAddress',
  browserInfo: 'browserInfo',
  isActive: 'isActive',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LibraryTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  sector: 'sector',
  country: 'country',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.LibraryQuestionScalarFieldEnum = {
  id: 'id',
  libraryTemplateId: 'libraryTemplateId',
  label: 'label',
  inputType: 'inputType',
  hint: 'hint',
  placeholder: 'placeholder',
  isRequired: 'isRequired',
  position: 'position',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  libraryTemplateQuestionGroupId: 'libraryTemplateQuestionGroupId'
};

exports.Prisma.LibraryQuestionOptionScalarFieldEnum = {
  id: 'id',
  label: 'label',
  code: 'code',
  libraryQuestionId: 'libraryQuestionId',
  nextLibraryQuestionId: 'nextLibraryQuestionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LibraryQuestionConditionScalarFieldEnum = {
  id: 'id',
  operator: 'operator',
  value: 'value',
  libraryQuestionId: 'libraryQuestionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LibraryTemplateQuestionGroupScalarFieldEnum = {
  id: 'id',
  title: 'title',
  order: 'order',
  libraryTemplateId: 'libraryTemplateId',
  parentGroupId: 'parentGroupId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectUserScalarFieldEnum = {
  id: 'id',
  permission: 'permission',
  userId: 'userId',
  projectId: 'projectId',
  createdAt: 'createdAt',
  updatedAT: 'updatedAT'
};

exports.Prisma.FormSubmissionScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  userId: 'userId',
  submittedAt: 'submittedAt',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  durationSeconds: 'durationSeconds',
  status: 'status',
  deviceInfo: 'deviceInfo',
  location: 'location',
  loginRequired: 'loginRequired',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AnswerScalarFieldEnum = {
  id: 'id',
  formSubmissionId: 'formSubmissionId',
  questionId: 'questionId',
  value: 'value',
  answerType: 'answerType',
  imageUrl: 'imageUrl',
  questionOptionId: 'questionOptionId',
  isOtherOption: 'isOtherOption',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ExportedFileScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  userId: 'userId',
  fileName: 'fileName',
  fileType: 'fileType',
  contentType: 'contentType',
  fileBuffer: 'fileBuffer',
  createdAt: 'createdAt'
};

exports.Prisma.LibraryQuestionBlockQuestionScalarFieldEnum = {
  id: 'id',
  label: 'label',
  inputType: 'inputType',
  hint: 'hint',
  placeholder: 'placeholder',
  isRequired: 'isRequired',
  position: 'position',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  libraryQuestionBlockQuestionGroupId: 'libraryQuestionBlockQuestionGroupId'
};

exports.Prisma.LibraryQuestionBlockQuestionOptionScalarFieldEnum = {
  id: 'id',
  label: 'label',
  code: 'code',
  nextLibraryQuestionId: 'nextLibraryQuestionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  libraryQuestionBlockQuestionId: 'libraryQuestionBlockQuestionId'
};

exports.Prisma.LibraryQuestionBlockQuestionConditionScalarFieldEnum = {
  id: 'id',
  operator: 'operator',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  libraryQuestionBlockQuestionId: 'libraryQuestionBlockQuestionId'
};

exports.Prisma.LibraryQuestionBlockQuestionGroupScalarFieldEnum = {
  id: 'id',
  title: 'title',
  order: 'order',
  parentGroupId: 'parentGroupId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TableColumnScalarFieldEnum = {
  id: 'id',
  questionId: 'questionId',
  columnName: 'columnName',
  parentColumnId: 'parentColumnId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TableRowScalarFieldEnum = {
  id: 'id',
  rowsName: 'rowsName',
  questionId: 'questionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ColumnRowScalarFieldEnum = {
  id: 'id',
  columnId: 'columnId',
  rowsId: 'rowsId',
  value: 'value',
  code: 'code',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.Sector = exports.$Enums.Sector = {
  information_media: 'information_media',
  econommic_social_development: 'econommic_social_development',
  security_police_peacekeeping: 'security_police_peacekeeping',
  disarmament_and_demobilization: 'disarmament_and_demobilization',
  environment: 'environment',
  private_sector: 'private_sector',
  humanitarian_coordination_information_management: 'humanitarian_coordination_information_management',
  humanitarian_multiple_clusters: 'humanitarian_multiple_clusters',
  humanitarian_camp_management_and_coordination: 'humanitarian_camp_management_and_coordination',
  humanitarian_early_recovery: 'humanitarian_early_recovery',
  humanitarian_education: 'humanitarian_education',
  humanitarian_emergency_shelter: 'humanitarian_emergency_shelter',
  humanitarian_emergency_telecoms: 'humanitarian_emergency_telecoms',
  humanitarian_food_security: 'humanitarian_food_security',
  humanitarian_health: 'humanitarian_health',
  humanitarian_logistics: 'humanitarian_logistics',
  humanitarian_nutrition: 'humanitarian_nutrition',
  humanitarian_protection: 'humanitarian_protection',
  humanitarian_sanitation_water_and_hygiene: 'humanitarian_sanitation_water_and_hygiene',
  other: 'other'
};

exports.OrganizationType = exports.$Enums.OrganizationType = {
  i_am_not_associated_with_any_organization: 'i_am_not_associated_with_any_organization',
  non_profit_organization: 'non_profit_organization',
  government_institution: 'government_institution',
  educational_organization: 'educational_organization',
  a_commercial_or_for_profit_company: 'a_commercial_or_for_profit_company'
};

exports.Status = exports.$Enums.Status = {
  deployed: 'deployed',
  archived: 'archived',
  draft: 'draft'
};

exports.InputType = exports.$Enums.InputType = {
  selectone: 'selectone',
  selectmany: 'selectmany',
  text: 'text',
  number: 'number',
  decimal: 'decimal',
  date: 'date',
  dateandtime: 'dateandtime',
  table: 'table'
};

exports.Operator = exports.$Enums.Operator = {
  is_less_than: 'is_less_than',
  is_greater_than: 'is_greater_than',
  is_equal_to: 'is_equal_to',
  less_than_or_equal_to: 'less_than_or_equal_to',
  greater_than_or_equal_to: 'greater_than_or_equal_to'
};

exports.Prisma.ModelName = {
  User: 'User',
  Project: 'Project',
  Question: 'Question',
  QuestionOption: 'QuestionOption',
  QuestionCondition: 'QuestionCondition',
  QuestionGroup: 'QuestionGroup',
  UserSession: 'UserSession',
  LibraryTemplate: 'LibraryTemplate',
  LibraryQuestion: 'LibraryQuestion',
  LibraryQuestionOption: 'LibraryQuestionOption',
  LibraryQuestionCondition: 'LibraryQuestionCondition',
  LibraryTemplateQuestionGroup: 'LibraryTemplateQuestionGroup',
  ProjectUser: 'ProjectUser',
  FormSubmission: 'FormSubmission',
  Answer: 'Answer',
  ExportedFile: 'ExportedFile',
  LibraryQuestionBlockQuestion: 'LibraryQuestionBlockQuestion',
  LibraryQuestionBlockQuestionOption: 'LibraryQuestionBlockQuestionOption',
  LibraryQuestionBlockQuestionCondition: 'LibraryQuestionBlockQuestionCondition',
  LibraryQuestionBlockQuestionGroup: 'LibraryQuestionBlockQuestionGroup',
  TableColumn: 'TableColumn',
  TableRow: 'TableRow',
  ColumnRow: 'ColumnRow'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
