import { Question } from "@/types/formBuilder";
import { QuestionBlockListColumns } from "@/components/tables/columns/QuestionBlockListColumns";
import { QuestionListTable } from "../tables/QuestionListTable";
import { useState } from "react";

interface QuestionBlockListProps {
  questions: Question[];
}

export const QuestionBlockList = ({ questions }: QuestionBlockListProps) => {
  const [globalFilter, setGlobalFilter] = useState("");

  return (
    <div className="space-y-4">
      <QuestionListTable
        columns={QuestionBlockListColumns}
        data={questions}
        globalFilter={globalFilter}
        setGlobalFilter={setGlobalFilter}
      />
    </div>
  );
}; 