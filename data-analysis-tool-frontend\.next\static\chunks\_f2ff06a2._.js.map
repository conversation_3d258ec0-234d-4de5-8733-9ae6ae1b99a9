{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/TableDataViewModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\n\r\ninterface TableDataItem {\r\n  column: string;\r\n  row: string;\r\n  value: string;\r\n}\r\n\r\ninterface CellValue {\r\n  columnId: number;\r\n  rowsId: number;\r\n  value: string;\r\n}\r\n\r\ninterface TableColumn {\r\n  id: number;\r\n  columnName: string;\r\n  parentColumnId?: number;\r\n}\r\n\r\ninterface TableRowType {\r\n  id: number;\r\n  rowsName: string;\r\n}\r\n\r\ninterface TableViewModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  tableData: TableDataItem[];\r\n  uniqueColumns: string[];\r\n  uniqueRows: string[];\r\n  rowsMap: Map<string, Map<string, string>>;\r\n  useParentChildColumns?: boolean;\r\n  loading?: boolean;\r\n  tableStructure?: {\r\n    tableColumns?: TableColumn[];\r\n    tableRows?: TableRowType[];\r\n  };\r\n}\r\n\r\nconst TableDataViewModal: React.FC<TableViewModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  tableData,\r\n  uniqueColumns,\r\n  uniqueRows,\r\n  rowsMap,\r\n  useParentChildColumns = false,\r\n  loading = false,\r\n  tableStructure,\r\n}) => {\r\n  // Parse the submitted table data to get the actual cell values\r\n  const parsedTableData = React.useMemo(() => {\r\n    if (!tableData || tableData.length === 0) return [];\r\n\r\n    // Convert tableData to CellValue format\r\n    const cellValues: CellValue[] = [];\r\n    tableData.forEach((item) => {\r\n      // Try to parse column and row as numbers (IDs)\r\n      const columnId = parseInt(item.column);\r\n      const rowId = parseInt(item.row);\r\n\r\n      if (!isNaN(columnId) && !isNaN(rowId)) {\r\n        cellValues.push({\r\n          columnId,\r\n          rowsId: rowId,\r\n          value: item.value,\r\n        });\r\n      }\r\n    });\r\n\r\n    return cellValues;\r\n  }, [tableData]);\r\n\r\n  // Group columns by parent-child relationships (same logic as TableInput.tsx)\r\n  const groupedColumns = React.useMemo(() => {\r\n    if (\r\n      !tableStructure?.tableColumns ||\r\n      tableStructure.tableColumns.length === 0\r\n    ) {\r\n      return {\r\n        parentColumns: [],\r\n        columnMap: new Map<number, TableColumn[]>(),\r\n        hasChildColumns: false,\r\n      };\r\n    }\r\n\r\n    // Get all parent columns (those without a parentColumnId)\r\n    const parentColumns = tableStructure.tableColumns.filter(\r\n      (col) => col.parentColumnId === undefined || col.parentColumnId === null\r\n    );\r\n\r\n    // Create a map of parent columns to their child columns\r\n    const columnMap = new Map<number, TableColumn[]>();\r\n\r\n    parentColumns.forEach((parentCol) => {\r\n      // Find all child columns for this parent\r\n      const childColumns = tableStructure.tableColumns!.filter(\r\n        (col) => col.parentColumnId === parentCol.id\r\n      );\r\n      columnMap.set(parentCol.id, childColumns);\r\n    });\r\n\r\n    // Check if any parent has child columns\r\n    const hasChildColumns = parentColumns.some(\r\n      (p) => (columnMap.get(p.id) || []).length > 0\r\n    );\r\n\r\n    return { parentColumns, columnMap, hasChildColumns };\r\n  }, [tableStructure]);\r\n\r\n  // Create a map for quick cell value lookup\r\n  const cellValueMap = React.useMemo(() => {\r\n    const map = new Map<string, string>();\r\n    parsedTableData.forEach((cell) => {\r\n      map.set(`${cell.columnId}_${cell.rowsId}`, cell.value);\r\n    });\r\n    return map;\r\n  }, [parsedTableData]);\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      className=\"p-6 rounded-md max-w-4xl w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%]\"\r\n    >\r\n      <div className=\"flex flex-col gap-4\">\r\n        <h2 className=\"text-xl font-semibold text-neutral-700\">{title}</h2>\r\n\r\n        {loading ? (\r\n          <div className=\"flex items-center justify-center p-8\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500\"></div>\r\n            <span className=\"ml-2 text-neutral-600\">Loading table data...</span>\r\n          </div>\r\n        ) : !tableStructure?.tableColumns ||\r\n          tableStructure.tableColumns.length === 0 ? (\r\n          <div className=\"py-4 text-center text-amber-600\">\r\n            <p>No table structure available.</p>\r\n            <p className=\"text-sm mt-2\">Debug info:</p>\r\n            <pre className=\"text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40\">\r\n              {JSON.stringify(\r\n                {\r\n                  hasTableStructure: !!tableStructure,\r\n                  tableColumnsLength: tableStructure?.tableColumns?.length || 0,\r\n                  tableRowsLength: tableStructure?.tableRows?.length || 0,\r\n                  tableDataLength: tableData?.length || 0,\r\n                  useParentChildColumns,\r\n                },\r\n                null,\r\n                2\r\n              )}\r\n            </pre>\r\n          </div>\r\n        ) : (\r\n          <div className=\"overflow-auto max-h-[70vh]\">\r\n            <Table className=\"border-collapse border border-amber-700\">\r\n              <TableHeader className=\"bg-amber-100\">\r\n                {/* First row: Parent column headers */}\r\n                <TableRow>\r\n                  <TableHead\r\n                    className=\"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-100\"\r\n                    rowSpan={groupedColumns.hasChildColumns ? 2 : 1}\r\n                  >\r\n                    {title}\r\n                  </TableHead>\r\n                  {groupedColumns.parentColumns.map((parentCol) => {\r\n                    const childColumns =\r\n                      groupedColumns.columnMap.get(parentCol.id) || [];\r\n                    // If this parent has children, it spans multiple columns\r\n                    const colSpan = childColumns.length || 1;\r\n\r\n                    return (\r\n                      <TableHead\r\n                        key={parentCol.id}\r\n                        colSpan={colSpan}\r\n                        className=\"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider text-center border border-amber-700 bg-amber-100\"\r\n                      >\r\n                        {parentCol.columnName}\r\n                      </TableHead>\r\n                    );\r\n                  })}\r\n                </TableRow>\r\n\r\n                {/* Second row: Child column headers (only if there are child columns) */}\r\n                {groupedColumns.hasChildColumns && (\r\n                  <TableRow>\r\n                    {groupedColumns.parentColumns.map((parentCol) => {\r\n                      const childColumns =\r\n                        groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                      // If this parent has no children, render a placeholder\r\n                      if (childColumns.length === 0) {\r\n                        return null; // Don't render anything for parents without children\r\n                      }\r\n\r\n                      // Otherwise, render each child column\r\n                      return childColumns.map((childCol) => (\r\n                        <TableHead\r\n                          key={childCol.id}\r\n                          className=\"px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-50\"\r\n                        >\r\n                          {childCol.columnName}\r\n                        </TableHead>\r\n                      ));\r\n                    })}\r\n                  </TableRow>\r\n                )}\r\n              </TableHeader>\r\n\r\n              <TableBody>\r\n                {tableStructure.tableRows?.map((row, rowIndex) => (\r\n                  <TableRow key={row.id} className=\"bg-white\">\r\n                    <TableCell className=\"px-3 py-2 text-xs font-medium border border-amber-700 bg-amber-50\">\r\n                      {row.rowsName}\r\n                    </TableCell>\r\n\r\n                    {/* Render cells for each parent column */}\r\n                    {groupedColumns.parentColumns.map((parentCol) => {\r\n                      const childColumns =\r\n                        groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                      // If this parent has no children, render a single cell\r\n                      if (childColumns.length === 0) {\r\n                        return (\r\n                          <TableCell\r\n                            key={`cell-${parentCol.id}-${row.id}`}\r\n                            className=\"px-3 py-2 text-xs border border-amber-700\"\r\n                          >\r\n                            {cellValueMap.get(`${parentCol.id}_${row.id}`) ||\r\n                              \"\"}\r\n                          </TableCell>\r\n                        );\r\n                      }\r\n\r\n                      // Otherwise, render cells for each child column\r\n                      return childColumns.map((childCol) => (\r\n                        <TableCell\r\n                          key={`cell-${childCol.id}-${row.id}`}\r\n                          className=\"px-3 py-2 text-xs border border-amber-700\"\r\n                        >\r\n                          {cellValueMap.get(`${childCol.id}_${row.id}`) || \"\"}\r\n                        </TableCell>\r\n                      ));\r\n                    })}\r\n                  </TableRow>\r\n                ))}\r\n              </TableBody>\r\n            </Table>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"flex justify-end mt-4\">\r\n          <button\r\n            onClick={onClose}\r\n            className=\"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors\"\r\n          >\r\n            Close\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default TableDataViewModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAoDA,MAAM,qBAAoD,CAAC,EACzD,MAAM,EACN,OAAO,EACP,KAAK,EACL,SAAS,EACT,aAAa,EACb,UAAU,EACV,OAAO,EACP,wBAAwB,KAAK,EAC7B,UAAU,KAAK,EACf,cAAc,EACf;;IACC,+DAA+D;IAC/D,MAAM,kBAAkB,6JAAA,CAAA,UAAK,CAAC,OAAO;uDAAC;YACpC,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG,OAAO,EAAE;YAEnD,wCAAwC;YACxC,MAAM,aAA0B,EAAE;YAClC,UAAU,OAAO;+DAAC,CAAC;oBACjB,+CAA+C;oBAC/C,MAAM,WAAW,SAAS,KAAK,MAAM;oBACrC,MAAM,QAAQ,SAAS,KAAK,GAAG;oBAE/B,IAAI,CAAC,MAAM,aAAa,CAAC,MAAM,QAAQ;wBACrC,WAAW,IAAI,CAAC;4BACd;4BACA,QAAQ;4BACR,OAAO,KAAK,KAAK;wBACnB;oBACF;gBACF;;YAEA,OAAO;QACT;sDAAG;QAAC;KAAU;IAEd,6EAA6E;IAC7E,MAAM,iBAAiB,6JAAA,CAAA,UAAK,CAAC,OAAO;sDAAC;YACnC,IACE,CAAC,gBAAgB,gBACjB,eAAe,YAAY,CAAC,MAAM,KAAK,GACvC;gBACA,OAAO;oBACL,eAAe,EAAE;oBACjB,WAAW,IAAI;oBACf,iBAAiB;gBACnB;YACF;YAEA,0DAA0D;YAC1D,MAAM,gBAAgB,eAAe,YAAY,CAAC,MAAM;4EACtD,CAAC,MAAQ,IAAI,cAAc,KAAK,aAAa,IAAI,cAAc,KAAK;;YAGtE,wDAAwD;YACxD,MAAM,YAAY,IAAI;YAEtB,cAAc,OAAO;8DAAC,CAAC;oBACrB,yCAAyC;oBACzC,MAAM,eAAe,eAAe,YAAY,CAAE,MAAM;mFACtD,CAAC,MAAQ,IAAI,cAAc,KAAK,UAAU,EAAE;;oBAE9C,UAAU,GAAG,CAAC,UAAU,EAAE,EAAE;gBAC9B;;YAEA,wCAAwC;YACxC,MAAM,kBAAkB,cAAc,IAAI;8EACxC,CAAC,IAAM,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG;;YAG9C,OAAO;gBAAE;gBAAe;gBAAW;YAAgB;QACrD;qDAAG;QAAC;KAAe;IAEnB,2CAA2C;IAC3C,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,OAAO;oDAAC;YACjC,MAAM,MAAM,IAAI;YAChB,gBAAgB,OAAO;4DAAC,CAAC;oBACvB,IAAI,GAAG,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,EAAE,KAAK,KAAK;gBACvD;;YACA,OAAO;QACT;mDAAG;QAAC;KAAgB;IACpB,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0C;;;;;;gBAEvD,wBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;2BAExC,CAAC,gBAAgB,gBACnB,eAAe,YAAY,CAAC,MAAM,KAAK,kBACvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,6LAAC;4BAAI,WAAU;sCACZ,KAAK,SAAS,CACb;gCACE,mBAAmB,CAAC,CAAC;gCACrB,oBAAoB,gBAAgB,cAAc,UAAU;gCAC5D,iBAAiB,gBAAgB,WAAW,UAAU;gCACtD,iBAAiB,WAAW,UAAU;gCACtC;4BACF,GACA,MACA;;;;;;;;;;;yCAKN,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,6LAAC,6HAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC,6HAAA,CAAA,WAAQ;;0DACP,6LAAC,6HAAA,CAAA,YAAS;gDACR,WAAU;gDACV,SAAS,eAAe,eAAe,GAAG,IAAI;0DAE7C;;;;;;4CAEF,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gDACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gDAClD,yDAAyD;gDACzD,MAAM,UAAU,aAAa,MAAM,IAAI;gDAEvC,qBACE,6LAAC,6HAAA,CAAA,YAAS;oDAER,SAAS;oDACT,WAAU;8DAET,UAAU,UAAU;mDAJhB,UAAU,EAAE;;;;;4CAOvB;;;;;;;oCAID,eAAe,eAAe,kBAC7B,6LAAC,6HAAA,CAAA,WAAQ;kDACN,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;4CACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;4CAElD,uDAAuD;4CACvD,IAAI,aAAa,MAAM,KAAK,GAAG;gDAC7B,OAAO,MAAM,qDAAqD;4CACpE;4CAEA,sCAAsC;4CACtC,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;oDAER,WAAU;8DAET,SAAS,UAAU;mDAHf,SAAS,EAAE;;;;;wCAMtB;;;;;;;;;;;;0CAKN,6LAAC,6HAAA,CAAA,YAAS;0CACP,eAAe,SAAS,EAAE,IAAI,CAAC,KAAK,yBACnC,6LAAC,6HAAA,CAAA,WAAQ;wCAAc,WAAU;;0DAC/B,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,IAAI,QAAQ;;;;;;4CAId,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gDACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gDAElD,uDAAuD;gDACvD,IAAI,aAAa,MAAM,KAAK,GAAG;oDAC7B,qBACE,6LAAC,6HAAA,CAAA,YAAS;wDAER,WAAU;kEAET,aAAa,GAAG,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,KAC3C;uDAJG,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;gDAO3C;gDAEA,gDAAgD;gDAChD,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;wDAER,WAAU;kEAET,aAAa,GAAG,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK;uDAH5C,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;4CAM1C;;uCAhCa,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;8BAwC/B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;GA9NM;KAAA;uCAgOS", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%28main%29/project/%5BhashedId%5D/data/table/columns.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ColumnDef } from \"@tanstack/react-table\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { ArrowUpDown, Eye } from \"lucide-react\";\r\nimport { formatDate } from \"@/lib/utils\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport TableDataViewModal from \"@/components/modals/TableDataViewModal\";\r\nimport axios from \"@/lib/axios\";\r\nimport { BiSolidEdit } from \"react-icons/bi\";\r\n\r\n// Type for a single submission\r\nexport type Submission = {\r\n  id?: number;\r\n\r\n  answers: {\r\n    id: number;\r\n    value: string | number;\r\n    question: {\r\n      inputType: string;\r\n      id: number;\r\n      label: string;\r\n      type?: string;\r\n    };\r\n  }[];\r\n  submissionTime?: string;\r\n  submittedBy?: string;\r\n  user?: {\r\n    id: number;\r\n    name: string;\r\n    email: string;\r\n  };\r\n};\r\n\r\n// Function to format cell value based on data type\r\nconst formatCellValue = (value: any, type?: string): string => {\r\n  if (value === null || value === undefined) return \"-\";\r\n\r\n  if (typeof value === \"boolean\") {\r\n    return value ? \"Yes\" : \"No\";\r\n  }\r\n\r\n  if (value instanceof Date) {\r\n    return formatDate(value);\r\n  }\r\n\r\n  if (type === \"date\" && typeof value === \"string\") {\r\n    try {\r\n      return formatDate(new Date(value));\r\n    } catch {\r\n      return value;\r\n    }\r\n  }\r\n\r\n  return String(value);\r\n};\r\n\r\n// Function to fetch table structure (columns and rows) from the backend\r\nconst fetchTableStructure = async (questionId: number) => {\r\n  try {\r\n    let tableData = null;\r\n\r\n    // Use the table-questions endpoint (the working one)\r\n    try {\r\n      const { data } = await axios.get(`/table-questions/${questionId}`);\r\n      if (data && data.success && data.data) {\r\n        // The response structure is { success: true, data: { question: {...}, cellValues: {...} } }\r\n        tableData = data.data.question;\r\n      } else {\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error fetching from /table-questions/ endpoint:\", err);\r\n    }\r\n\r\n    // If we still don't have tableRows, try to fetch them separately\r\n    if (tableData && tableData.tableColumns && !tableData.tableRows) {\r\n      try {\r\n        const { data } = await axios.get(`/table-rows/${questionId}`);\r\n        if (data && data.data && data.data.tableRows) {\r\n          tableData.tableRows = data.data.tableRows;\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching table rows separately:\", err);\r\n      }\r\n    }\r\n\r\n    if (!tableData) {\r\n      // Return a default structure instead of null to prevent errors\r\n      return {\r\n        id: questionId,\r\n        label: \"Table Data\",\r\n        tableColumns: [],\r\n        tableRows: [\r\n          { id: 1, rowsName: \"Row 1\" },\r\n          { id: 2, rowsName: \"Row 2\" },\r\n          { id: 3, rowsName: \"Row 3\" },\r\n        ],\r\n      };\r\n    }\r\n\r\n    // Ensure tableColumns exists and flatten nested structure\r\n    if (!tableData.tableColumns || !Array.isArray(tableData.tableColumns)) {\r\n      console.error(\r\n        \"tableColumns is missing or not an array, creating default tableColumns\"\r\n      );\r\n      tableData.tableColumns = [];\r\n    } else {\r\n      // Flatten the nested structure to include child columns\r\n      const flattenedColumns: any[] = [];\r\n\r\n      tableData.tableColumns.forEach((column: any) => {\r\n        // Add the parent column\r\n        flattenedColumns.push({\r\n          id: column.id,\r\n          columnName: column.columnName,\r\n          parentColumnId: column.parentColumnId || null,\r\n        });\r\n\r\n        // Add child columns if they exist\r\n        if (column.childColumns && Array.isArray(column.childColumns)) {\r\n          column.childColumns.forEach((childColumn: any) => {\r\n            flattenedColumns.push({\r\n              id: childColumn.id,\r\n              columnName: childColumn.columnName,\r\n              parentColumnId: childColumn.parentColumnId || column.id,\r\n            });\r\n          });\r\n        }\r\n      });\r\n\r\n      tableData.tableColumns = flattenedColumns;\r\n    }\r\n\r\n    // Ensure tableRows exists\r\n    if (!tableData.tableRows || !Array.isArray(tableData.tableRows)) {\r\n      console.error(\r\n        \"tableRows is missing or not an array, creating default tableRows\"\r\n      );\r\n\r\n      // Create dummy tableRows if none exist and tableColumns exists\r\n      if (tableData.tableColumns && tableData.tableColumns.length > 0) {\r\n        tableData.tableRows = tableData.tableColumns.map(\r\n          (col: { id: number; columnName: string }) => ({\r\n            id: col.id,\r\n            rowsName: `Row ${col.id}`,\r\n          })\r\n        );\r\n      } else {\r\n        // If tableColumns doesn't exist or is empty, create a default tableRows array\r\n        tableData.tableRows = [\r\n          { id: 1, rowsName: \"Row 1\" },\r\n          { id: 2, rowsName: \"Row 2\" },\r\n          { id: 3, rowsName: \"Row 3\" },\r\n        ];\r\n      }\r\n    }\r\n\r\n    return tableData;\r\n  } catch (error) {\r\n    console.error(\"Error fetching table structure:\", error);\r\n    // Return a default structure instead of null to prevent errors\r\n    const defaultStructure = {\r\n      id: questionId,\r\n      label: \"Table Data\",\r\n      tableColumns: [],\r\n      tableRows: [\r\n        { id: 1, rowsName: \"Row 1\" },\r\n        { id: 2, rowsName: \"Row 2\" },\r\n        { id: 3, rowsName: \"Row 3\" },\r\n      ],\r\n    };\r\n    return defaultStructure;\r\n  }\r\n};\r\n\r\n// Helper function to generate columns from a sample submission\r\nexport const generateColumns = (\r\n  onViewSubmission: (submission: Submission) => void,\r\n  Submission?: Submission,\r\n  hashedId?: string,\r\n  allQuestions?: any[] // All questions from the project including conditional ones\r\n): ColumnDef<Submission>[] => {\r\n  // Base column for ID or index\r\n  const baseColumns: ColumnDef<Submission>[] = [\r\n    {\r\n      id: \"select\",\r\n      header: ({ table }) => (\r\n        <Checkbox\r\n          className=\"w-5 h-5 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer\"\r\n          checked={\r\n            table.getIsAllPageRowsSelected() ||\r\n            (table.getIsSomePageRowsSelected() && \"indeterminate\")\r\n          }\r\n          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}\r\n          aria-label=\"Select all\"\r\n        />\r\n      ),\r\n      cell: ({ row }) => (\r\n        <Checkbox\r\n          className=\"w-5 h-5 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 cursor-pointer\"\r\n          checked={row.getIsSelected()}\r\n          onCheckedChange={(value) => row.toggleSelected(!!value)}\r\n          aria-label=\"Select row\"\r\n        />\r\n      ),\r\n      enableHiding: false,\r\n    },\r\n    {\r\n      id: \"id\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <div className=\"flex items-center hover:text-neutral-300\">\r\n            <span>ID</span>\r\n            <span className=\"ml-2 flex-shrink-0 w-4 h-4\">\r\n              <ArrowUpDown\r\n                className=\"w-full h-full\"\r\n                onClick={() =>\r\n                  column.toggleSorting(column.getIsSorted() === \"asc\")\r\n                }\r\n              />\r\n            </span>\r\n          </div>\r\n        );\r\n      },\r\n      accessorFn: (_, rowIndex) => rowIndex + 1, // Use row index as ID\r\n      enableSorting: true,\r\n      cell: ({ row }) => {\r\n        return (\r\n          <div className=\"flex items-center gap-8 font-medium text-neutral-700\">\r\n            {row.index + 1}\r\n            <span className=\"flex items-center gap-2\">\r\n              <Eye\r\n                onClick={() => onViewSubmission(row.original)}\r\n                className=\"w-4 h-4 cursor-pointer hover:text-primary-500\"\r\n              />\r\n\r\n              <BiSolidEdit\r\n                className=\"w-4 h-4 cursor-pointer hover:text-primary-500\"\r\n                title=\"Edit\"\r\n                onClick={() => {\r\n                  const submissionId = row.original.id;\r\n                  if (!submissionId || !hashedId) return;\r\n                  window.open(\r\n                    `/edit-submission/${hashedId}/${submissionId}`,\r\n                    \"_blank\"\r\n                  );\r\n                }}\r\n              />\r\n            </span>\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      id: \"validation\",\r\n      header: \"Validation\",\r\n      accessorKey: \"validation\",\r\n    },\r\n  ];\r\n\r\n  // Use allQuestions if provided, otherwise fall back to questions from submissions\r\n  let questionsToUse: any[] = [];\r\n\r\n  if (allQuestions && allQuestions.length > 0) {\r\n    // Use ALL questions from the project (including conditional ones)\r\n    questionsToUse = allQuestions;\r\n  } else if (Submission && Submission.answers?.length) {\r\n    // Fallback: use unique questions from submissions (old behavior)\r\n    questionsToUse = Array.from(\r\n      new Map(\r\n        Submission.answers.map((a) => [a.question.id, a.question])\r\n      ).values()\r\n    );\r\n  } else {\r\n    // No questions available\r\n    return baseColumns;\r\n  }\r\n\r\n  // Generate dynamic question columns\r\n  const questionColumns: ColumnDef<Submission>[] = questionsToUse.map(\r\n    (question) => ({\r\n      id: `${question.label}`,\r\n      header: ({ column }) => (\r\n        <div className=\"flex items-center justify-between\">\r\n          <span>{question.label}</span>\r\n          <ArrowUpDown\r\n            className=\"ml-1 h-4 w-4 cursor-pointer opacity-60\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === \"asc\")}\r\n          />\r\n        </div>\r\n      ),\r\n      accessorFn: (row) => {\r\n        // Filter answers that match the question.id\r\n        const matches = row.answers.filter(\r\n          (a) => a.question?.id === question.id\r\n        );\r\n\r\n        // If no matches found, this question wasn't answered (likely due to conditional logic)\r\n        if (matches.length === 0) {\r\n          console.error(\r\n            `No answer found for question \"${question.label}\" (ID: ${question.id}) in submission ${row.id} - likely conditional question not triggered`\r\n          );\r\n          return null; // Will be displayed as \"-\" or \"N/A\"\r\n        }\r\n\r\n        // Handle selectmany vs normal input\r\n        if (question.inputType === \"selectmany\") {\r\n          // For selectmany, each selected option creates a separate answer record\r\n          // We need to collect all the values and join them properly\r\n          const values = matches\r\n            .map((m) => m.value)\r\n            .filter((v) => v && String(v).trim() !== \"\") // Filter out empty values\r\n            .sort(); // Sort for consistent display\r\n          return values.length > 0 ? values.join(\", \") : null;\r\n        }\r\n\r\n        // For selectone and other input types, return the first (and should be only) value\r\n        return matches[0]?.value ?? null;\r\n      },\r\n      cell: ({ getValue }) => {\r\n        const value = getValue();\r\n\r\n        // Handle table input type differently\r\n        if (question.inputType === \"table\") {\r\n          try {\r\n            // First, ensure we're working with a string\r\n            let valueStr = typeof value === \"string\" ? value : String(value);\r\n\r\n            // Check if the value is already a JSON string\r\n            let tableData;\r\n\r\n            // Try to parse if it looks like JSON\r\n            if (valueStr.startsWith(\"[\") && valueStr.includes(\"{\")) {\r\n              try {\r\n                tableData = JSON.parse(valueStr);\r\n              } catch (parseError) {\r\n                console.error(\"Failed to parse JSON string:\", parseError);\r\n\r\n                // If it's a malformed JSON string, try to clean it up\r\n                // This handles cases where the JSON might have extra quotes or escaping\r\n                valueStr = valueStr\r\n                  .replace(/\\\\\"/g, '\"')\r\n                  .replace(/^\"/, \"\")\r\n                  .replace(/\"$/, \"\");\r\n\r\n                try {\r\n                  tableData = JSON.parse(valueStr);\r\n                } catch (secondParseError) {\r\n                  console.error(\r\n                    \"Failed second parse attempt:\",\r\n                    secondParseError\r\n                  );\r\n\r\n                  // If it still fails, try to extract the array from the string\r\n                  const match = valueStr.match(/\\[([\\s\\S]*)\\]/);\r\n                  if (match && match[0]) {\r\n                    try {\r\n                      tableData = JSON.parse(match[0]);\r\n                    } catch (thirdParseError) {\r\n                      console.error(\r\n                        \"Failed third parse attempt:\",\r\n                        thirdParseError\r\n                      );\r\n\r\n                      // Special handling for the format in the screenshot\r\n                      // Format: [{'columnId':18,'rowsId':15,'value':'Ram Babu Thapa'},...\r\n                      try {\r\n                        // Convert single quotes to double quotes for valid JSON\r\n                        const fixedStr = match[0].replace(/'/g, '\"');\r\n                        tableData = JSON.parse(fixedStr);\r\n                      } catch (fourthParseError) {\r\n                        console.error(\r\n                          \"Failed fourth parse attempt:\",\r\n                          fourthParseError\r\n                        );\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n\r\n            // If we still don't have valid data, try to parse it as a custom format\r\n            if (\r\n              !tableData &&\r\n              valueStr.includes(\"columnId\") &&\r\n              valueStr.includes(\"rowsId\")\r\n            ) {\r\n              try {\r\n                // Handle the format: [{'columnId':18,'rowsId':15,'value':'Ram Babu Thapa'},...]\r\n                // Convert to valid JSON by replacing single quotes with double quotes\r\n                const fixedStr = valueStr.replace(/'/g, '\"');\r\n                tableData = JSON.parse(fixedStr);\r\n              } catch (customFormatError) {\r\n                console.error(\r\n                  \"Failed custom format parsing:\",\r\n                  customFormatError\r\n                );\r\n              }\r\n            }\r\n\r\n            // If we have valid array data, display it as a table\r\n            if (Array.isArray(tableData)) {\r\n              // Process the table data to extract column and row information\r\n              const processedData = tableData.map((item) => {\r\n                // Get column name - use the actual column name if available\r\n                let columnName = \"\";\r\n                if (item.columnName) {\r\n                  // If columnName is directly available in the data, use it\r\n                  columnName = item.columnName;\r\n                } else if (item.column && item.column.columnName) {\r\n                  // If the column object is available with columnName property\r\n                  columnName = item.column.columnName;\r\n                } else if (item.columnId) {\r\n                  // If we only have columnId, check if we have a name for it\r\n                  // First, try to get the name from the item itself if it has a name property\r\n                  if (item.name) {\r\n                    columnName = item.name;\r\n                  } else if (item.label) {\r\n                    columnName = item.label;\r\n                  } else {\r\n                    // For now, store the columnId but we'll replace it with the actual name later\r\n                    // We'll use the columnId as a key to look up the actual name when we fetch the table structure\r\n                    columnName = String(item.columnId);\r\n                  }\r\n                }\r\n\r\n                // Get row name - use the actual row name if available\r\n                let rowName = \"\";\r\n                if (item.rowsName) {\r\n                  // If rowsName is directly available in the data, use it\r\n                  rowName = item.rowsName;\r\n                } else if (item.row && item.row.rowsName) {\r\n                  // If the row object is available with rowsName property\r\n                  rowName = item.row.rowsName;\r\n                } else if (item.rowsId) {\r\n                  // If we only have rowsId, check if we have a name for it\r\n                  // First, try to get the name from the item itself if it has a name property\r\n                  if (item.name) {\r\n                    rowName = item.name;\r\n                  } else if (item.label) {\r\n                    rowName = item.label;\r\n                  } else {\r\n                    // Store the rowsId as a key to look up the actual name later\r\n                    // We'll replace it with the actual name when we fetch the table structure\r\n                    rowName = String(item.rowsId);\r\n                  }\r\n                }\r\n\r\n                // Extract the value\r\n                const cellValue = item.value !== undefined ? item.value : \"\";\r\n\r\n                return {\r\n                  column: columnName,\r\n                  row: rowName,\r\n                  value: cellValue,\r\n                };\r\n              });\r\n\r\n              // Create a structured table with proper headers in the format of the second image\r\n              // Group data by rows and columns to create a matrix\r\n              const rowsMap = new Map<string, Map<string, string>>();\r\n              const columnsSet = new Set<string>();\r\n\r\n              // First pass: collect all unique rows and columns\r\n              processedData.forEach((item) => {\r\n                columnsSet.add(String(item.column));\r\n                if (!rowsMap.has(String(item.row))) {\r\n                  rowsMap.set(String(item.row), new Map<string, string>());\r\n                }\r\n                rowsMap\r\n                  .get(String(item.row))\r\n                  ?.set(String(item.column), String(item.value));\r\n              });\r\n\r\n              // Convert to arrays for rendering\r\n              const uniqueColumns = Array.from(columnsSet);\r\n              const uniqueRows = Array.from(rowsMap.keys());\r\n\r\n              // Check if we should use the parent-child column structure\r\n              // This should be determined by the table structure from the backend\r\n              // Set to true to use the parent-child column structure\r\n              const useParentChildColumns = true;\r\n\r\n              // Create a TableCellWithModal component to handle state\r\n              const TableCellWithModal = () => {\r\n                const [showModal, setShowModal] = useState(false);\r\n                const [tableStructure, setTableStructure] = useState<any>(null);\r\n                const [loading, setLoading] = useState(false);\r\n\r\n                // Debug effect to track tableStructure changes\r\n                useEffect(() => {}, [tableStructure]);\r\n\r\n                // Function to fetch table structure when the modal is opened\r\n                const fetchTableData = async () => {\r\n                  if (!question.id) {\r\n                    console.error(\"No question ID available\");\r\n                    return;\r\n                  }\r\n\r\n                  setLoading(true);\r\n                  try {\r\n                    const structure = await fetchTableStructure(question.id);\r\n\r\n                    if (structure) {\r\n                      // Make sure tableRows is included in the structure\r\n                      if (!structure.tableRows) {\r\n                        console.error(\r\n                          \"tableRows is missing from the structure!\"\r\n                        );\r\n                      }\r\n                      setTableStructure(structure);\r\n\r\n                      // Update the processed data with actual column and row names\r\n                      if (structure.tableColumns && structure.tableRows) {\r\n                        // Create maps for quick lookup\r\n                        const columnMap = new Map();\r\n                        const rowMap = new Map();\r\n\r\n                        structure.tableColumns.forEach((col: any) => {\r\n                          columnMap.set(col.id, col.columnName);\r\n                          // Also map the string version of the ID\r\n                          columnMap.set(String(col.id), col.columnName);\r\n                        });\r\n\r\n                        structure.tableRows.forEach((row: any) => {\r\n                          rowMap.set(row.id, row.rowsName);\r\n                          // Also map the string version of the ID\r\n                          rowMap.set(String(row.id), row.rowsName);\r\n                        });\r\n\r\n                        // Update the processed data\r\n                        processedData.forEach((item) => {\r\n                          if (item.column) {\r\n                            // Check if the column is a number (ID) or already a name\r\n                            if (!isNaN(Number(item.column))) {\r\n                              const columnId = item.column;\r\n\r\n                              if (columnMap.has(columnId)) {\r\n                                const oldColumn = item.column;\r\n                                item.column = columnMap.get(columnId);\r\n                              } else {\r\n                                // If we don't have a mapping for this column ID, try to find a column with this ID\r\n                                const matchingColumn =\r\n                                  structure.tableColumns.find(\r\n                                    (col: any) =>\r\n                                      String(col.id) === String(columnId)\r\n                                  );\r\n                                if (matchingColumn) {\r\n                                  const oldColumn = item.column;\r\n                                  item.column = matchingColumn.columnName;\r\n                                }\r\n                              }\r\n                            }\r\n                          }\r\n\r\n                          if (item.row) {\r\n                            // Check if the row is a number (ID) or already a name\r\n                            if (!isNaN(Number(item.row))) {\r\n                              const rowId = item.row;\r\n\r\n                              if (rowMap.has(rowId)) {\r\n                                const oldRow = item.row;\r\n                                item.row = rowMap.get(rowId);\r\n                              } else {\r\n                                // If we don't have a mapping for this row ID, try to find a row with this ID\r\n                                const matchingRow = structure.tableRows.find(\r\n                                  (row: any) => String(row.id) === String(rowId)\r\n                                );\r\n                                if (matchingRow) {\r\n                                  const oldRow = item.row;\r\n                                  item.row = matchingRow.rowsName;\r\n                                }\r\n                              }\r\n                            }\r\n                          }\r\n                        });\r\n\r\n                        // Rebuild the rowsMap with the updated column and row names\r\n                        const newRowsMap = new Map<\r\n                          string,\r\n                          Map<string, string>\r\n                        >();\r\n                        const newColumnsSet = new Set<string>();\r\n\r\n                        // First pass: collect all unique rows and columns with their updated names\r\n                        processedData.forEach((item) => {\r\n                          newColumnsSet.add(String(item.column));\r\n                          if (!newRowsMap.has(String(item.row))) {\r\n                            newRowsMap.set(\r\n                              String(item.row),\r\n                              new Map<string, string>()\r\n                            );\r\n                          }\r\n                          newRowsMap\r\n                            .get(String(item.row))\r\n                            ?.set(String(item.column), String(item.value));\r\n                        });\r\n\r\n                        // Update the original arrays with the transformed data\r\n                        uniqueColumns.length = 0;\r\n                        uniqueRows.length = 0;\r\n\r\n                        // Add the updated column and row names\r\n                        newColumnsSet.forEach((col) => uniqueColumns.push(col));\r\n                        newRowsMap.forEach((_, row) => uniqueRows.push(row));\r\n\r\n                        // Clear the existing rowsMap and copy entries from newRowsMap\r\n                        rowsMap.clear();\r\n                        newRowsMap.forEach((valueMap, key) => {\r\n                          rowsMap.set(key, valueMap);\r\n                        });\r\n\r\n                        // Log the final rowsMap for debugging\r\n                      }\r\n                    }\r\n                  } catch (error) {\r\n                    console.error(\"Error fetching table structure:\", error);\r\n                  } finally {\r\n                    setLoading(false);\r\n                  }\r\n                };\r\n\r\n                return (\r\n                  <div className=\"font-medium text-neutral-700\">\r\n                    <a\r\n                      href=\"#\"\r\n                      onClick={async (e) => {\r\n                        e.preventDefault();\r\n                        setLoading(true);\r\n                        setShowModal(true);\r\n                        await fetchTableData(); // Wait for table structure to load\r\n                      }}\r\n                      className=\"inline-flex items-center gap-1 text-primary-500 hover:text-primary-700 hover:underline whitespace-nowrap\"\r\n                    >\r\n                      <Eye size={12} className=\"inline\" /> Click to view table\r\n                    </a>\r\n\r\n                    {/* Debug tableStructure */}\r\n                    {(() => {\r\n                      return null;\r\n                    })()}\r\n                    <TableDataViewModal\r\n                      isOpen={showModal}\r\n                      onClose={() => setShowModal(false)}\r\n                      title={question.label || \"Table Data\"}\r\n                      tableData={processedData}\r\n                      uniqueColumns={uniqueColumns}\r\n                      uniqueRows={uniqueRows}\r\n                      rowsMap={rowsMap}\r\n                      useParentChildColumns={useParentChildColumns}\r\n                      loading={loading}\r\n                      tableStructure={tableStructure}\r\n                    />\r\n                  </div>\r\n                );\r\n              };\r\n\r\n              return <TableCellWithModal />;\r\n            }\r\n          } catch (e) {\r\n            // If parsing fails, log the error and the value that caused it\r\n            console.error(\"Error parsing table data:\", e, \"Value:\", value);\r\n          }\r\n        }\r\n\r\n        // Default rendering for non-table data or if table parsing failed\r\n        // Handle null/undefined values for conditional questions\r\n        if (value === null || value === undefined || value === \"\") {\r\n          return <div className=\"font-medium text-neutral-400 italic\">-</div>;\r\n        }\r\n\r\n        return (\r\n          <div className=\"font-medium text-neutral-700\">{String(value)}</div>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n    })\r\n  );\r\n\r\n  // Optional metadata columns\r\n  const rearColumns: ColumnDef<Submission>[] = [\r\n    {\r\n      id: \"submissionTime\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <div className=\"flex items-center gap-4 hover:text-neutral-300\">\r\n            <span>Submission Time</span>\r\n            <span className=\"ml-2 flex-shrink-0 w-4 h-4\">\r\n              <ArrowUpDown\r\n                className=\"w-full h-full\"\r\n                onClick={() =>\r\n                  column.toggleSorting(column.getIsSorted() === \"asc\")\r\n                }\r\n              />\r\n            </span>\r\n          </div>\r\n        );\r\n      },\r\n      accessorKey: \"submissionTime\",\r\n      cell: ({ getValue }) => {\r\n        const value = getValue();\r\n        return (\r\n          <div className=\"font-medium text-neutral-700\">\r\n            {formatCellValue(value, \"date\") || \"Not recorded\"}\r\n          </div>\r\n        );\r\n      },\r\n      enableSorting: true,\r\n    },\r\n    {\r\n      id: \"submittedBy\",\r\n      header: ({ column }) => {\r\n        return (\r\n          <div className=\"flex items-center gap-4 hover:text-neutral-300\">\r\n            <span>Submitted By</span>\r\n            <span className=\"ml-2 flex-shrink-0 w-4 h-4\">\r\n              <ArrowUpDown\r\n                className=\"w-full h-full\"\r\n                onClick={() =>\r\n                  column.toggleSorting(column.getIsSorted() === \"asc\")\r\n                }\r\n              />\r\n            </span>\r\n          </div>\r\n        );\r\n      },\r\n      accessorKey: \"submittedBy\",\r\n      accessorFn: (row) => row.user?.name || \"Anonymous\",\r\n      cell: ({ getValue }) => {\r\n        const value = getValue() as string;\r\n        return <div className=\"font-medium text-neutral-700\">{value}</div>;\r\n      },\r\n      enableSorting: true,\r\n    },\r\n  ];\r\n\r\n  return [...baseColumns, ...questionColumns, ...rearColumns];\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAkCA,mDAAmD;AACnD,MAAM,kBAAkB,CAAC,OAAY;IACnC,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAElD,IAAI,OAAO,UAAU,WAAW;QAC9B,OAAO,QAAQ,QAAQ;IACzB;IAEA,IAAI,iBAAiB,MAAM;QACzB,OAAO,CAAA,GAAA,+GAAA,CAAA,aAAU,AAAD,EAAE;IACpB;IAEA,IAAI,SAAS,UAAU,OAAO,UAAU,UAAU;QAChD,IAAI;YACF,OAAO,CAAA,GAAA,+GAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK;QAC7B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,OAAO,OAAO;AAChB;AAEA,wEAAwE;AACxE,MAAM,sBAAsB,OAAO;IACjC,IAAI;QACF,IAAI,YAAY;QAEhB,qDAAqD;QACrD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,YAAY;YACjE,IAAI,QAAQ,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBACrC,4FAA4F;gBAC5F,YAAY,KAAK,IAAI,CAAC,QAAQ;YAChC,OAAO,CACP;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mDAAmD;QACnE;QAEA,iEAAiE;QACjE,IAAI,aAAa,UAAU,YAAY,IAAI,CAAC,UAAU,SAAS,EAAE;YAC/D,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,YAAY;gBAC5D,IAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE;oBAC5C,UAAU,SAAS,GAAG,KAAK,IAAI,CAAC,SAAS;gBAC3C;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;QAEA,IAAI,CAAC,WAAW;YACd,+DAA+D;YAC/D,OAAO;gBACL,IAAI;gBACJ,OAAO;gBACP,cAAc,EAAE;gBAChB,WAAW;oBACT;wBAAE,IAAI;wBAAG,UAAU;oBAAQ;oBAC3B;wBAAE,IAAI;wBAAG,UAAU;oBAAQ;oBAC3B;wBAAE,IAAI;wBAAG,UAAU;oBAAQ;iBAC5B;YACH;QACF;QAEA,0DAA0D;QAC1D,IAAI,CAAC,UAAU,YAAY,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU,YAAY,GAAG;YACrE,QAAQ,KAAK,CACX;YAEF,UAAU,YAAY,GAAG,EAAE;QAC7B,OAAO;YACL,wDAAwD;YACxD,MAAM,mBAA0B,EAAE;YAElC,UAAU,YAAY,CAAC,OAAO,CAAC,CAAC;gBAC9B,wBAAwB;gBACxB,iBAAiB,IAAI,CAAC;oBACpB,IAAI,OAAO,EAAE;oBACb,YAAY,OAAO,UAAU;oBAC7B,gBAAgB,OAAO,cAAc,IAAI;gBAC3C;gBAEA,kCAAkC;gBAClC,IAAI,OAAO,YAAY,IAAI,MAAM,OAAO,CAAC,OAAO,YAAY,GAAG;oBAC7D,OAAO,YAAY,CAAC,OAAO,CAAC,CAAC;wBAC3B,iBAAiB,IAAI,CAAC;4BACpB,IAAI,YAAY,EAAE;4BAClB,YAAY,YAAY,UAAU;4BAClC,gBAAgB,YAAY,cAAc,IAAI,OAAO,EAAE;wBACzD;oBACF;gBACF;YACF;YAEA,UAAU,YAAY,GAAG;QAC3B;QAEA,0BAA0B;QAC1B,IAAI,CAAC,UAAU,SAAS,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU,SAAS,GAAG;YAC/D,QAAQ,KAAK,CACX;YAGF,+DAA+D;YAC/D,IAAI,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC/D,UAAU,SAAS,GAAG,UAAU,YAAY,CAAC,GAAG,CAC9C,CAAC,MAA4C,CAAC;wBAC5C,IAAI,IAAI,EAAE;wBACV,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;oBAC3B,CAAC;YAEL,OAAO;gBACL,8EAA8E;gBAC9E,UAAU,SAAS,GAAG;oBACpB;wBAAE,IAAI;wBAAG,UAAU;oBAAQ;oBAC3B;wBAAE,IAAI;wBAAG,UAAU;oBAAQ;oBAC3B;wBAAE,IAAI;wBAAG,UAAU;oBAAQ;iBAC5B;YACH;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,+DAA+D;QAC/D,MAAM,mBAAmB;YACvB,IAAI;YACJ,OAAO;YACP,cAAc,EAAE;YAChB,WAAW;gBACT;oBAAE,IAAI;oBAAG,UAAU;gBAAQ;gBAC3B;oBAAE,IAAI;oBAAG,UAAU;gBAAQ;gBAC3B;oBAAE,IAAI;oBAAG,UAAU;gBAAQ;aAC5B;QACH;QACA,OAAO;IACT;AACF;AAGO,MAAM,kBAAkB,CAC7B,kBACA,YACA,UACA,aAAqB,4DAA4D;;IAEjF,8BAA8B;IAC9B,MAAM,cAAuC;QAC3C;YACE,IAAI;YACJ,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,gIAAA,CAAA,WAAQ;oBACP,WAAU;oBACV,SACE,MAAM,wBAAwB,MAC7B,MAAM,yBAAyB,MAAM;oBAExC,iBAAiB,CAAC,QAAU,MAAM,yBAAyB,CAAC,CAAC,CAAC;oBAC9D,cAAW;;;;;;YAGf,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC,gIAAA,CAAA,WAAQ;oBACP,WAAU;oBACV,SAAS,IAAI,aAAa;oBAC1B,iBAAiB,CAAC,QAAU,IAAI,cAAc,CAAC,CAAC,CAAC;oBACjD,cAAW;;;;;;YAGf,cAAc;QAChB;QACA;YACE,IAAI;YACJ,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAK;;;;;;sCACN,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC,2NAAA,CAAA,cAAW;gCACV,WAAU;gCACV,SAAS,IACP,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;;;;;;;;;;;;;;;;YAM1D;YACA,YAAY,CAAC,GAAG,WAAa,WAAW;YACxC,eAAe;YACf,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,qBACE,6LAAC;oBAAI,WAAU;;wBACZ,IAAI,KAAK,GAAG;sCACb,6LAAC;4BAAK,WAAU;;8CACd,6LAAC,mMAAA,CAAA,MAAG;oCACF,SAAS,IAAM,iBAAiB,IAAI,QAAQ;oCAC5C,WAAU;;;;;;8CAGZ,6LAAC,iJAAA,CAAA,cAAW;oCACV,WAAU;oCACV,OAAM;oCACN,SAAS;wCACP,MAAM,eAAe,IAAI,QAAQ,CAAC,EAAE;wCACpC,IAAI,CAAC,gBAAgB,CAAC,UAAU;wCAChC,OAAO,IAAI,CACT,CAAC,iBAAiB,EAAE,SAAS,CAAC,EAAE,cAAc,EAC9C;oCAEJ;;;;;;;;;;;;;;;;;;YAKV;QACF;QACA;YACE,IAAI;YACJ,QAAQ;YACR,aAAa;QACf;KACD;IAED,kFAAkF;IAClF,IAAI,iBAAwB,EAAE;IAE9B,IAAI,gBAAgB,aAAa,MAAM,GAAG,GAAG;QAC3C,kEAAkE;QAClE,iBAAiB;IACnB,OAAO,IAAI,cAAc,WAAW,OAAO,EAAE,QAAQ;QACnD,iEAAiE;QACjE,iBAAiB,MAAM,IAAI,CACzB,IAAI,IACF,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,IAAM;gBAAC,EAAE,QAAQ,CAAC,EAAE;gBAAE,EAAE,QAAQ;aAAC,GACzD,MAAM;IAEZ,OAAO;QACL,yBAAyB;QACzB,OAAO;IACT;IAEA,oCAAoC;IACpC,MAAM,kBAA2C,eAAe,GAAG,CACjE,CAAC,WAAa,CAAC;YACb,IAAI,GAAG,SAAS,KAAK,EAAE;YACvB,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAM,SAAS,KAAK;;;;;;sCACrB,6LAAC,2NAAA,CAAA,cAAW;4BACV,WAAU;4BACV,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;;;;;;;;;;;YAInE,YAAY,CAAC;gBACX,4CAA4C;gBAC5C,MAAM,UAAU,IAAI,OAAO,CAAC,MAAM,CAChC,CAAC,IAAM,EAAE,QAAQ,EAAE,OAAO,SAAS,EAAE;gBAGvC,uFAAuF;gBACvF,IAAI,QAAQ,MAAM,KAAK,GAAG;oBACxB,QAAQ,KAAK,CACX,CAAC,8BAA8B,EAAE,SAAS,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,gBAAgB,EAAE,IAAI,EAAE,CAAC,4CAA4C,CAAC;oBAE7I,OAAO,MAAM,oCAAoC;gBACnD;gBAEA,oCAAoC;gBACpC,IAAI,SAAS,SAAS,KAAK,cAAc;oBACvC,wEAAwE;oBACxE,2DAA2D;oBAC3D,MAAM,SAAS,QACZ,GAAG,CAAC,CAAC,IAAM,EAAE,KAAK,EAClB,MAAM,CAAC,CAAC,IAAM,KAAK,OAAO,GAAG,IAAI,OAAO,IAAI,0BAA0B;qBACtE,IAAI,IAAI,8BAA8B;oBACzC,OAAO,OAAO,MAAM,GAAG,IAAI,OAAO,IAAI,CAAC,QAAQ;gBACjD;gBAEA,mFAAmF;gBACnF,OAAO,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9B;YACA,MAAM,CAAC,EAAE,QAAQ,EAAE;gBACjB,MAAM,QAAQ;gBAEd,sCAAsC;gBACtC,IAAI,SAAS,SAAS,KAAK,SAAS;oBAClC,IAAI;wBACF,4CAA4C;wBAC5C,IAAI,WAAW,OAAO,UAAU,WAAW,QAAQ,OAAO;wBAE1D,8CAA8C;wBAC9C,IAAI;wBAEJ,qCAAqC;wBACrC,IAAI,SAAS,UAAU,CAAC,QAAQ,SAAS,QAAQ,CAAC,MAAM;4BACtD,IAAI;gCACF,YAAY,KAAK,KAAK,CAAC;4BACzB,EAAE,OAAO,YAAY;gCACnB,QAAQ,KAAK,CAAC,gCAAgC;gCAE9C,sDAAsD;gCACtD,wEAAwE;gCACxE,WAAW,SACR,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,MAAM,IACd,OAAO,CAAC,MAAM;gCAEjB,IAAI;oCACF,YAAY,KAAK,KAAK,CAAC;gCACzB,EAAE,OAAO,kBAAkB;oCACzB,QAAQ,KAAK,CACX,gCACA;oCAGF,8DAA8D;oCAC9D,MAAM,QAAQ,SAAS,KAAK,CAAC;oCAC7B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;wCACrB,IAAI;4CACF,YAAY,KAAK,KAAK,CAAC,KAAK,CAAC,EAAE;wCACjC,EAAE,OAAO,iBAAiB;4CACxB,QAAQ,KAAK,CACX,+BACA;4CAGF,oDAAoD;4CACpD,oEAAoE;4CACpE,IAAI;gDACF,wDAAwD;gDACxD,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM;gDACxC,YAAY,KAAK,KAAK,CAAC;4CACzB,EAAE,OAAO,kBAAkB;gDACzB,QAAQ,KAAK,CACX,gCACA;4CAEJ;wCACF;oCACF;gCACF;4BACF;wBACF;wBAEA,wEAAwE;wBACxE,IACE,CAAC,aACD,SAAS,QAAQ,CAAC,eAClB,SAAS,QAAQ,CAAC,WAClB;4BACA,IAAI;gCACF,gFAAgF;gCAChF,sEAAsE;gCACtE,MAAM,WAAW,SAAS,OAAO,CAAC,MAAM;gCACxC,YAAY,KAAK,KAAK,CAAC;4BACzB,EAAE,OAAO,mBAAmB;gCAC1B,QAAQ,KAAK,CACX,iCACA;4BAEJ;wBACF;wBAEA,qDAAqD;wBACrD,IAAI,MAAM,OAAO,CAAC,YAAY;;4BAC5B,+DAA+D;4BAC/D,MAAM,gBAAgB,UAAU,GAAG,CAAC,CAAC;gCACnC,4DAA4D;gCAC5D,IAAI,aAAa;gCACjB,IAAI,KAAK,UAAU,EAAE;oCACnB,0DAA0D;oCAC1D,aAAa,KAAK,UAAU;gCAC9B,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,UAAU,EAAE;oCAChD,6DAA6D;oCAC7D,aAAa,KAAK,MAAM,CAAC,UAAU;gCACrC,OAAO,IAAI,KAAK,QAAQ,EAAE;oCACxB,2DAA2D;oCAC3D,4EAA4E;oCAC5E,IAAI,KAAK,IAAI,EAAE;wCACb,aAAa,KAAK,IAAI;oCACxB,OAAO,IAAI,KAAK,KAAK,EAAE;wCACrB,aAAa,KAAK,KAAK;oCACzB,OAAO;wCACL,8EAA8E;wCAC9E,+FAA+F;wCAC/F,aAAa,OAAO,KAAK,QAAQ;oCACnC;gCACF;gCAEA,sDAAsD;gCACtD,IAAI,UAAU;gCACd,IAAI,KAAK,QAAQ,EAAE;oCACjB,wDAAwD;oCACxD,UAAU,KAAK,QAAQ;gCACzB,OAAO,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,QAAQ,EAAE;oCACxC,wDAAwD;oCACxD,UAAU,KAAK,GAAG,CAAC,QAAQ;gCAC7B,OAAO,IAAI,KAAK,MAAM,EAAE;oCACtB,yDAAyD;oCACzD,4EAA4E;oCAC5E,IAAI,KAAK,IAAI,EAAE;wCACb,UAAU,KAAK,IAAI;oCACrB,OAAO,IAAI,KAAK,KAAK,EAAE;wCACrB,UAAU,KAAK,KAAK;oCACtB,OAAO;wCACL,6DAA6D;wCAC7D,0EAA0E;wCAC1E,UAAU,OAAO,KAAK,MAAM;oCAC9B;gCACF;gCAEA,oBAAoB;gCACpB,MAAM,YAAY,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,GAAG;gCAE1D,OAAO;oCACL,QAAQ;oCACR,KAAK;oCACL,OAAO;gCACT;4BACF;4BAEA,kFAAkF;4BAClF,oDAAoD;4BACpD,MAAM,UAAU,IAAI;4BACpB,MAAM,aAAa,IAAI;4BAEvB,kDAAkD;4BAClD,cAAc,OAAO,CAAC,CAAC;gCACrB,WAAW,GAAG,CAAC,OAAO,KAAK,MAAM;gCACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO,KAAK,GAAG,IAAI;oCAClC,QAAQ,GAAG,CAAC,OAAO,KAAK,GAAG,GAAG,IAAI;gCACpC;gCACA,QACG,GAAG,CAAC,OAAO,KAAK,GAAG,IAClB,IAAI,OAAO,KAAK,MAAM,GAAG,OAAO,KAAK,KAAK;4BAChD;4BAEA,kCAAkC;4BAClC,MAAM,gBAAgB,MAAM,IAAI,CAAC;4BACjC,MAAM,aAAa,MAAM,IAAI,CAAC,QAAQ,IAAI;4BAE1C,2DAA2D;4BAC3D,oEAAoE;4BACpE,uDAAuD;4BACvD,MAAM,wBAAwB;4BAE9B,wDAAwD;4BACxD,MAAM,qBAAqB;;gCACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;gCAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;gCAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;gCAEvC,+CAA+C;gCAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oGAAE,KAAO;mGAAG;oCAAC;iCAAe;gCAEpC,6DAA6D;gCAC7D,MAAM,iBAAiB;oCACrB,IAAI,CAAC,SAAS,EAAE,EAAE;wCAChB,QAAQ,KAAK,CAAC;wCACd;oCACF;oCAEA,WAAW;oCACX,IAAI;wCACF,MAAM,YAAY,MAAM,oBAAoB,SAAS,EAAE;wCAEvD,IAAI,WAAW;4CACb,mDAAmD;4CACnD,IAAI,CAAC,UAAU,SAAS,EAAE;gDACxB,QAAQ,KAAK,CACX;4CAEJ;4CACA,kBAAkB;4CAElB,6DAA6D;4CAC7D,IAAI,UAAU,YAAY,IAAI,UAAU,SAAS,EAAE;gDACjD,+BAA+B;gDAC/B,MAAM,YAAY,IAAI;gDACtB,MAAM,SAAS,IAAI;gDAEnB,UAAU,YAAY,CAAC,OAAO,CAAC,CAAC;oDAC9B,UAAU,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,UAAU;oDACpC,wCAAwC;oDACxC,UAAU,GAAG,CAAC,OAAO,IAAI,EAAE,GAAG,IAAI,UAAU;gDAC9C;gDAEA,UAAU,SAAS,CAAC,OAAO,CAAC,CAAC;oDAC3B,OAAO,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,QAAQ;oDAC/B,wCAAwC;oDACxC,OAAO,GAAG,CAAC,OAAO,IAAI,EAAE,GAAG,IAAI,QAAQ;gDACzC;gDAEA,4BAA4B;gDAC5B,cAAc,OAAO,CAAC,CAAC;oDACrB,IAAI,KAAK,MAAM,EAAE;wDACf,yDAAyD;wDACzD,IAAI,CAAC,MAAM,OAAO,KAAK,MAAM,IAAI;4DAC/B,MAAM,WAAW,KAAK,MAAM;4DAE5B,IAAI,UAAU,GAAG,CAAC,WAAW;gEAC3B,MAAM,YAAY,KAAK,MAAM;gEAC7B,KAAK,MAAM,GAAG,UAAU,GAAG,CAAC;4DAC9B,OAAO;gEACL,mFAAmF;gEACnF,MAAM,iBACJ,UAAU,YAAY,CAAC,IAAI,CACzB,CAAC,MACC,OAAO,IAAI,EAAE,MAAM,OAAO;gEAEhC,IAAI,gBAAgB;oEAClB,MAAM,YAAY,KAAK,MAAM;oEAC7B,KAAK,MAAM,GAAG,eAAe,UAAU;gEACzC;4DACF;wDACF;oDACF;oDAEA,IAAI,KAAK,GAAG,EAAE;wDACZ,sDAAsD;wDACtD,IAAI,CAAC,MAAM,OAAO,KAAK,GAAG,IAAI;4DAC5B,MAAM,QAAQ,KAAK,GAAG;4DAEtB,IAAI,OAAO,GAAG,CAAC,QAAQ;gEACrB,MAAM,SAAS,KAAK,GAAG;gEACvB,KAAK,GAAG,GAAG,OAAO,GAAG,CAAC;4DACxB,OAAO;gEACL,6EAA6E;gEAC7E,MAAM,cAAc,UAAU,SAAS,CAAC,IAAI,CAC1C,CAAC,MAAa,OAAO,IAAI,EAAE,MAAM,OAAO;gEAE1C,IAAI,aAAa;oEACf,MAAM,SAAS,KAAK,GAAG;oEACvB,KAAK,GAAG,GAAG,YAAY,QAAQ;gEACjC;4DACF;wDACF;oDACF;gDACF;gDAEA,4DAA4D;gDAC5D,MAAM,aAAa,IAAI;gDAIvB,MAAM,gBAAgB,IAAI;gDAE1B,2EAA2E;gDAC3E,cAAc,OAAO,CAAC,CAAC;oDACrB,cAAc,GAAG,CAAC,OAAO,KAAK,MAAM;oDACpC,IAAI,CAAC,WAAW,GAAG,CAAC,OAAO,KAAK,GAAG,IAAI;wDACrC,WAAW,GAAG,CACZ,OAAO,KAAK,GAAG,GACf,IAAI;oDAER;oDACA,WACG,GAAG,CAAC,OAAO,KAAK,GAAG,IAClB,IAAI,OAAO,KAAK,MAAM,GAAG,OAAO,KAAK,KAAK;gDAChD;gDAEA,uDAAuD;gDACvD,cAAc,MAAM,GAAG;gDACvB,WAAW,MAAM,GAAG;gDAEpB,uCAAuC;gDACvC,cAAc,OAAO,CAAC,CAAC,MAAQ,cAAc,IAAI,CAAC;gDAClD,WAAW,OAAO,CAAC,CAAC,GAAG,MAAQ,WAAW,IAAI,CAAC;gDAE/C,8DAA8D;gDAC9D,QAAQ,KAAK;gDACb,WAAW,OAAO,CAAC,CAAC,UAAU;oDAC5B,QAAQ,GAAG,CAAC,KAAK;gDACnB;4CAEA,sCAAsC;4CACxC;wCACF;oCACF,EAAE,OAAO,OAAO;wCACd,QAAQ,KAAK,CAAC,mCAAmC;oCACnD,SAAU;wCACR,WAAW;oCACb;gCACF;gCAEA,qBACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS,OAAO;gDACd,EAAE,cAAc;gDAChB,WAAW;gDACX,aAAa;gDACb,MAAM,kBAAkB,mCAAmC;4CAC7D;4CACA,WAAU;;8DAEV,6LAAC,mMAAA,CAAA,MAAG;oDAAC,MAAM;oDAAI,WAAU;;;;;;gDAAW;;;;;;;wCAIrC,CAAC;4CACA,OAAO;wCACT,CAAC;sDACD,6LAAC,8IAAA,CAAA,UAAkB;4CACjB,QAAQ;4CACR,SAAS,IAAM,aAAa;4CAC5B,OAAO,SAAS,KAAK,IAAI;4CACzB,WAAW;4CACX,eAAe;4CACf,YAAY;4CACZ,SAAS;4CACT,uBAAuB;4CACvB,SAAS;4CACT,gBAAgB;;;;;;;;;;;;4BAIxB;+BA3KM;4BA6KN,qBAAO,6LAAC;;;;;wBACV;oBACF,EAAE,OAAO,GAAG;wBACV,+DAA+D;wBAC/D,QAAQ,KAAK,CAAC,6BAA6B,GAAG,UAAU;oBAC1D;gBACF;gBAEA,kEAAkE;gBAClE,yDAAyD;gBACzD,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;oBACzD,qBAAO,6LAAC;wBAAI,WAAU;kCAAsC;;;;;;gBAC9D;gBAEA,qBACE,6LAAC;oBAAI,WAAU;8BAAgC,OAAO;;;;;;YAE1D;YACA,eAAe;QACjB,CAAC;IAGH,4BAA4B;IAC5B,MAAM,cAAuC;QAC3C;YACE,IAAI;YACJ,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAK;;;;;;sCACN,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC,2NAAA,CAAA,cAAW;gCACV,WAAU;gCACV,SAAS,IACP,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;;;;;;;;;;;;;;;;YAM1D;YACA,aAAa;YACb,MAAM,CAAC,EAAE,QAAQ,EAAE;gBACjB,MAAM,QAAQ;gBACd,qBACE,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,OAAO,WAAW;;;;;;YAGzC;YACA,eAAe;QACjB;QACA;YACE,IAAI;YACJ,QAAQ,CAAC,EAAE,MAAM,EAAE;gBACjB,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAK;;;;;;sCACN,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC,2NAAA,CAAA,cAAW;gCACV,WAAU;gCACV,SAAS,IACP,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;;;;;;;;;;;;;;;;YAM1D;YACA,aAAa;YACb,YAAY,CAAC,MAAQ,IAAI,IAAI,EAAE,QAAQ;YACvC,MAAM,CAAC,EAAE,QAAQ,EAAE;gBACjB,MAAM,QAAQ;gBACd,qBAAO,6LAAC;oBAAI,WAAU;8BAAgC;;;;;;YACxD;YACA,eAAe;QACjB;KACD;IAED,OAAO;WAAI;WAAgB;WAAoB;KAAY;AAC7D", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/submission.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\n// Delete form submission\r\nconst deleteFormSubmission = async (submissionId: number, projectId: number) => {\r\n  try {\r\n    const { data } = await axios.delete(`/form-submissions/${submissionId}?projectId=${projectId}`);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting form submission:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete multiple form submissions\r\nconst deleteMultipleFormSubmissions = async (submissionIds: number[], projectId: number) => {\r\n  try {\r\n    const deletePromises = submissionIds.map((id) =>\r\n      axios.delete(`/form-submissions/${id}?projectId=${projectId}`)\r\n    );\r\n    const results = await Promise.all(deletePromises);\r\n    return results.map((result) => result.data);\r\n  } catch (error) {\r\n    console.error(\"Error deleting multiple form submissions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update a single answer\r\nconst updateAnswer = async (\r\n  answerData: {\r\n    submissionId: number;\r\n    questionId: number | null | undefined;\r\n    answerType: string;\r\n    value: string | string[] | number;\r\n    questionOptionId?: number | number[] | null;\r\n  },\r\n  projectId: number\r\n) => {\r\n  try {\r\n    if (!answerData.submissionId || !answerData.questionId) {\r\n      throw new Error(\"submissionId and questionId are required\");\r\n    }\r\n\r\n    const formattedData = { ...answerData };\r\n    if (formattedData.questionOptionId === null) {\r\n      delete formattedData.questionOptionId;\r\n    } else if (Array.isArray(formattedData.questionOptionId)) {\r\n      formattedData.questionOptionId = formattedData.questionOptionId.filter((id) => id != null);\r\n      if (formattedData.questionOptionId.length === 0) {\r\n        delete formattedData.questionOptionId;\r\n      }\r\n    }\r\n\r\n    const { data } = await axios.patch(\r\n      `/answers/${answerData.questionId}?projectId=${projectId}`,\r\n      formattedData\r\n    );\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating answer:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update multiple answers for a single question (for select_many type)\r\nconst updateMultipleAnswers = async (answerData: {\r\n  submissionId: number;\r\n  questionId: number;\r\n  answerType: string;\r\n  value: string[];\r\n  questionOptionId?: number[];\r\n}) => {\r\n  try {\r\n    const { data } = await axios.patch(`/answers/${answerData.questionId}`, {\r\n      ...answerData,\r\n      answerType: \"selectmany\",\r\n    });\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating multiple answers:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update multiple answers using the /answers/multiple endpoint\r\nconst updateMultipleAnswersWithEndpoint = async (\r\n  answers: Array<{\r\n    id?: number;\r\n    questionId?: number;\r\n    projectId: number;\r\n    value: any;\r\n    answerType: string;\r\n    questionOptionId?: number | number[];\r\n    isOtherOption?: boolean;\r\n    formSubmissionId: number;\r\n  }>,\r\n  projectId: number\r\n) => {\r\n  try {\r\n    const { data } = await axios.patch(`/answers/multiple?projectId=${projectId}`, answers);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating multiple answers with endpoint:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport {\r\n  deleteFormSubmission,\r\n  deleteMultipleFormSubmissions,\r\n  updateAnswer,\r\n  updateMultipleAnswers,\r\n  updateMultipleAnswersWithEndpoint,\r\n};"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,yBAAyB;AACzB,MAAM,uBAAuB,OAAO,cAAsB;IACxD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,aAAa,WAAW,EAAE,WAAW;QAC9F,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEA,mCAAmC;AACnC,MAAM,gCAAgC,OAAO,eAAyB;IACpE,IAAI;QACF,MAAM,iBAAiB,cAAc,GAAG,CAAC,CAAC,KACxC,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,GAAG,WAAW,EAAE,WAAW;QAE/D,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;QAClC,OAAO,QAAQ,GAAG,CAAC,CAAC,SAAW,OAAO,IAAI;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,MAAM;IACR;AACF;AAEA,yBAAyB;AACzB,MAAM,eAAe,OACnB,YAOA;IAEA,IAAI;QACF,IAAI,CAAC,WAAW,YAAY,IAAI,CAAC,WAAW,UAAU,EAAE;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,gBAAgB;YAAE,GAAG,UAAU;QAAC;QACtC,IAAI,cAAc,gBAAgB,KAAK,MAAM;YAC3C,OAAO,cAAc,gBAAgB;QACvC,OAAO,IAAI,MAAM,OAAO,CAAC,cAAc,gBAAgB,GAAG;YACxD,cAAc,gBAAgB,GAAG,cAAc,gBAAgB,CAAC,MAAM,CAAC,CAAC,KAAO,MAAM;YACrF,IAAI,cAAc,gBAAgB,CAAC,MAAM,KAAK,GAAG;gBAC/C,OAAO,cAAc,gBAAgB;YACvC;QACF;QAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,CAAC,SAAS,EAAE,WAAW,UAAU,CAAC,WAAW,EAAE,WAAW,EAC1D;QAEF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEA,uEAAuE;AACvE,MAAM,wBAAwB,OAAO;IAOnC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,UAAU,EAAE,EAAE;YACtE,GAAG,UAAU;YACb,YAAY;QACd;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEA,+DAA+D;AAC/D,MAAM,oCAAoC,OACxC,SAUA;IAEA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,WAAW,EAAE;QAC/E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/EditSubmissionModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\n\r\nimport {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  useReactTable,\r\n} from \"@tanstack/react-table\";\r\n\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { updateAnswer } from \"@/lib/api/submission\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\n\r\nimport { Submission } from \"@/app/(main)/project/[hashedId]/data/table/columns\";\r\n\r\n// Transform submission data to a simpler format for the table\r\nconst transformSubmissionData = (submission: Submission) => {\r\n  if (!submission || !submission.answers) return [];\r\n\r\n  // Create a map to group answers by question\r\n  const questionMap = new Map();\r\n\r\n  submission.answers.forEach((answer) => {\r\n    const questionId = answer.question?.id;\r\n    const questionLabel = answer.question?.label || \"Unknown\";\r\n\r\n    if (!questionId) return;\r\n\r\n    if (!questionMap.has(questionId)) {\r\n      questionMap.set(questionId, {\r\n        type: answer.question?.inputType || \"text\",\r\n        question: questionLabel,\r\n        questionObject: answer.question,\r\n        answers: [answer.value],\r\n        originalData: [answer],\r\n      });\r\n    } else {\r\n      const existingEntry = questionMap.get(questionId);\r\n      existingEntry.answers.push(answer.value);\r\n      existingEntry.originalData.push(answer);\r\n    }\r\n  });\r\n\r\n  // Convert map to array\r\n  return Array.from(questionMap.values());\r\n};\r\n\r\nconst EditSubmissionModal = ({\r\n  showModal,\r\n  onClose,\r\n  onConfirm,\r\n  submission,\r\n  isMultipleSelection = false,\r\n  selectedSubmissions = [],\r\n  projectId,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  submission: Submission;\r\n  isMultipleSelection?: boolean;\r\n  selectedSubmissions?: Submission[];\r\n  projectId: number;\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [editingData, setEditingData] = useState<any>(null);\r\n  const [newResponse, setNewResponse] = useState(\"\");\r\n  const [selectedSubmissionId, setSelectedSubmissionId] = useState<\r\n    number | null\r\n  >(null);\r\n\r\n  // Add dispatch for notifications\r\n  const dispatch = useDispatch();\r\n  const queryClient = useQueryClient();\r\n\r\n  // Create refs for the input elements\r\n  const questionInputRef = useRef<HTMLInputElement>(null);\r\n  const answerInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Separate state for filtering\r\n  const [questionFilter, setQuestionFilter] = useState(\"\");\r\n  const [answerFilter, setAnswerFilter] = useState(\"\");\r\n\r\n  // Transform the submission data for display\r\n  const data = React.useMemo(() => {\r\n    if (isMultipleSelection && selectedSubmissionId) {\r\n      // Find the selected submission from the array of selected submissions\r\n      const selectedSubmission = selectedSubmissions.find(\r\n        (submission) => submission.id === selectedSubmissionId\r\n      );\r\n      return selectedSubmission\r\n        ? transformSubmissionData(selectedSubmission)\r\n        : [];\r\n    }\r\n    return transformSubmissionData(submission);\r\n  }, [\r\n    submission,\r\n    isMultipleSelection,\r\n    selectedSubmissionId,\r\n    selectedSubmissions,\r\n  ]);\r\n\r\n  // Set default selected submission when modal opens\r\n  useEffect(() => {\r\n    if (\r\n      isMultipleSelection &&\r\n      selectedSubmissions.length > 0 &&\r\n      !selectedSubmissionId\r\n    ) {\r\n      const firstId = selectedSubmissions[0]?.id;\r\n      if (firstId !== undefined) {\r\n        setSelectedSubmissionId(firstId);\r\n      }\r\n    }\r\n  }, [isMultipleSelection, selectedSubmissions, selectedSubmissionId]);\r\n\r\n  // Filter the data directly\r\n  const filteredData = React.useMemo(() => {\r\n    return data.filter((item) => {\r\n      const questionMatches =\r\n        item.question.toLowerCase().includes(questionFilter.toLowerCase()) ||\r\n        !questionFilter;\r\n      const answerMatches =\r\n        String(item.answers)\r\n          .toLowerCase()\r\n          .includes(answerFilter.toLowerCase()) || !answerFilter;\r\n      return questionMatches && answerMatches;\r\n    });\r\n  }, [data, questionFilter, answerFilter]);\r\n\r\n  // Handle submission selection change\r\n  const handleSubmissionChange = (\r\n    event: React.ChangeEvent<HTMLSelectElement>\r\n  ) => {\r\n    setSelectedSubmissionId(Number(event.target.value));\r\n  };\r\n\r\n  const table = useReactTable({\r\n    data: filteredData,\r\n    columns: [\r\n      {\r\n        accessorKey: \"type\",\r\n        header: \"Type\",\r\n      },\r\n      {\r\n        accessorKey: \"question\",\r\n        header: () => (\r\n          <div>\r\n            <div>Question</div>\r\n            <Input\r\n              ref={questionInputRef}\r\n              placeholder=\"Search questions...\"\r\n              value={questionFilter}\r\n              onChange={(e) => setQuestionFilter(e.target.value)}\r\n              className=\"bg-neutral-100 text-neutral-700 mt-2 h-8\"\r\n            />\r\n          </div>\r\n        ),\r\n      },\r\n      {\r\n        accessorKey: \"answers\",\r\n        header: () => (\r\n          <div>\r\n            <div>Answer</div>\r\n            <Input\r\n              ref={answerInputRef}\r\n              placeholder=\"Search answers...\"\r\n              value={answerFilter}\r\n              onChange={(e) => setAnswerFilter(e.target.value)}\r\n              className=\"bg-neutral-100 text-neutral-700 mt-2 h-8\"\r\n            />\r\n          </div>\r\n        ),\r\n        cell: ({ row }) => {\r\n          const answers = row.original.answers;\r\n\r\n          // If multiple submissions are selected, show special text\r\n          if (isMultipleSelection) {\r\n            return (\r\n              <div className=\"flex flex-col\">\r\n                <div className=\"text-neutral-800 italic\">\r\n                  Multiple responses\r\n                </div>\r\n              </div>\r\n            );\r\n          }\r\n\r\n          return (\r\n            <div className=\"flex flex-col\">\r\n              {answers.map((answer: any, index: number) => (\r\n                <div key={index} className={index > 0 ? \"\" : \"\"}>\r\n                  {String(answer)}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          );\r\n        },\r\n      },\r\n      {\r\n        accessorKey: \"action\",\r\n        header: \"Action\",\r\n        cell: ({ row }) => (\r\n          <button\r\n            className=\"btn-primary\"\r\n            onClick={() => handleEdit(row.original)}\r\n          >\r\n            Edit\r\n          </button>\r\n        ),\r\n      },\r\n    ],\r\n    getCoreRowModel: getCoreRowModel(),\r\n  });\r\n\r\n  const handleEdit = (rowData: any) => {\r\n    setIsEditing(true);\r\n    setEditingData(rowData);\r\n    // Join multiple answers with line breaks for editing\r\n    setNewResponse(rowData.answers.join(\"\\n\"));\r\n  };\r\n\r\n  // Define mutation for updating answers\r\n  const updateAnswerMutation = useMutation({\r\n    mutationFn: (data: {\r\n      submissionId: number;\r\n      questionId: number | null | undefined;\r\n      answerType: string;\r\n      value: string | string[] | number;\r\n      questionOptionId?: number | number[];\r\n    }) => {\r\n      // Make sure the questionId is valid\r\n      if (!data.questionId) {\r\n        throw new Error(\"Question ID is required\");\r\n      }\r\n\r\n      // Make sure the submissionId is valid\r\n      if (!data.submissionId) {\r\n        throw new Error(\"Submission ID is required\");\r\n      }\r\n\r\n      // Set up the payload according to backend expectations\r\n      const payload: {\r\n        submissionId: number;\r\n        questionId: number;\r\n        answerType: string;\r\n        value: any;\r\n        questionOptionId?: number | number[];\r\n      } = {\r\n        submissionId: data.submissionId,\r\n        questionId: data.questionId as number, // Type assertion since we checked it's not null\r\n        answerType: data.answerType,\r\n        value: data.value,\r\n      };\r\n\r\n      // Handle questionOptionId correctly based on answerType\r\n      if (data.answerType === \"selectmany\") {\r\n        // For selectmany, questionOptionId must be an array\r\n        payload.questionOptionId = Array.isArray(data.questionOptionId)\r\n          ? data.questionOptionId\r\n          : data.questionOptionId\r\n          ? [data.questionOptionId]\r\n          : []; // Convert to array if not already\r\n      } else if (data.questionOptionId !== undefined) {\r\n        // For other types, questionOptionId must NOT be an array\r\n        payload.questionOptionId = Array.isArray(data.questionOptionId)\r\n          ? data.questionOptionId[0] // Take first if it's incorrectly an array\r\n          : data.questionOptionId;\r\n      }\r\n\r\n      // Ensure value type matches answerType requirements\r\n      if (data.answerType === \"number\" || data.answerType === \"decimal\") {\r\n        // Ensure it's a number\r\n        payload.value =\r\n          typeof data.value === \"string\"\r\n            ? parseFloat(data.value)\r\n            : typeof data.value === \"number\"\r\n            ? data.value\r\n            : 0;\r\n      } else if (data.answerType === \"selectmany\") {\r\n        // For selectmany, value must be an array of strings\r\n        payload.value = Array.isArray(data.value)\r\n          ? data.value.map((v) => String(v))\r\n          : [String(data.value)];\r\n      } else {\r\n        // For all other types, ensure value is a string\r\n        payload.value = String(data.value);\r\n      }\r\n\r\n      // Send the request\r\n      return updateAnswer(payload, projectId);\r\n    },\r\n    onSuccess: (response) => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Answer updated successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      // Invalidate queries to refresh data\r\n      queryClient.invalidateQueries({ queryKey: [\"formSubmissions\"] });\r\n\r\n      // Reset edit state\r\n      setIsEditing(false);\r\n      setEditingData(null);\r\n      setNewResponse(\"\");\r\n\r\n      // Call onConfirm to close modal and clear selections\r\n      onConfirm();\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Error updating answer:\", error);\r\n\r\n      // Log full error details\r\n      console.error(\"Error details:\", {\r\n        response: error?.response?.data,\r\n        status: error?.response?.status,\r\n        headers: error?.response?.headers,\r\n      });\r\n\r\n      // Extract more specific error message if available\r\n      const errorMessage =\r\n        error?.response?.data?.message ||\r\n        error?.response?.data?.errors ||\r\n        \"Failed to update answer\";\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message:\r\n            typeof errorMessage === \"string\"\r\n              ? errorMessage\r\n              : \"Validation error in the form submission\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const handleSave = async () => {\r\n    if (!editingData) return;\r\n\r\n    // Get the current working submission\r\n    const currentSubmission =\r\n      isMultipleSelection && selectedSubmissionId\r\n        ? selectedSubmissions.find(\r\n            (submission) => submission.id === selectedSubmissionId\r\n          ) || submission\r\n        : submission;\r\n\r\n    // Check if submission has ID\r\n    if (!currentSubmission?.id) {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Submission ID is missing\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Split new response by line breaks to get individual answers\r\n    const newAnswers = newResponse\r\n      .split(\"\\n\")\r\n      .map((answer) => answer.trim())\r\n      .filter((answer) => answer);\r\n\r\n    // Skip if no answers\r\n    if (newAnswers.length === 0) {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Please enter a valid response\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    const questionId = editingData.questionObject?.id;\r\n    if (!questionId) {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question ID is missing\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Get question type\r\n    const answerType = editingData.type || \"text\";\r\n\r\n    try {\r\n      // Get question option IDs from original data if available\r\n      let questionOptionId;\r\n\r\n      if (answerType === \"selectmany\") {\r\n        // For selectmany, we need an array of option IDs\r\n        const optionIds = editingData.originalData\r\n          ?.map((item: any) => item.questionOptionId)\r\n          .filter(Boolean);\r\n\r\n        // If we don't have enough options, or any at all, create placeholders or use just the first one\r\n        if (!optionIds || optionIds.length === 0) {\r\n          questionOptionId = newAnswers.map(() => null);\r\n        } else if (optionIds.length !== newAnswers.length) {\r\n          // If counts don't match, use the available ones and null for the rest\r\n          questionOptionId = [...optionIds];\r\n          while (questionOptionId.length < newAnswers.length) {\r\n            questionOptionId.push(questionOptionId[0] || null);\r\n          }\r\n        } else {\r\n          questionOptionId = optionIds;\r\n        }\r\n      } else {\r\n        // For non-selectmany, use the first option ID if available\r\n        questionOptionId =\r\n          editingData.originalData?.[0]?.questionOptionId || null;\r\n      }\r\n\r\n      // Prepare the mutation data\r\n      const mutationData = {\r\n        submissionId: currentSubmission.id,\r\n        questionId: questionId,\r\n        answerType: answerType,\r\n        value: answerType === \"selectmany\" ? newAnswers : newAnswers[0],\r\n        questionOptionId: questionOptionId,\r\n      };\r\n\r\n      // Execute the mutation\r\n      updateAnswerMutation.mutate(mutationData);\r\n    } catch (error: any) {\r\n      console.error(\"Form validation error:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Please check your input and try again\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Modal\r\n        isOpen={showModal}\r\n        onClose={onClose}\r\n        className=\"flex flex-col gap-5 p-6 rounded-md\"\r\n      >\r\n        {!isEditing ? (\r\n          <>\r\n            <div className=\"flex flex-col gap-4 max-h-[500px] overflow-y-auto\">\r\n              <h2 className=\"text-lg font-semibold text-neutral-700\">\r\n                {isMultipleSelection\r\n                  ? \"Edit Selected Submission\"\r\n                  : \"Edit Submission\"}\r\n              </h2>\r\n\r\n              <div>\r\n                {/* Add submission selector dropdown when multiple are selected */}\r\n                {isMultipleSelection && selectedSubmissions.length > 0 && (\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <label\r\n                      htmlFor=\"submission-selector\"\r\n                      className=\"text-sm font-medium text-neutral-700\"\r\n                    >\r\n                      Select submission to edit:\r\n                    </label>\r\n                    <select\r\n                      id=\"submission-selector\"\r\n                      className=\"border border-neutral-300 rounded-md p-1 text-sm bg-white\"\r\n                      value={selectedSubmissionId || \"\"}\r\n                      onChange={handleSubmissionChange}\r\n                    >\r\n                      {selectedSubmissions.map((sub) => (\r\n                        <option key={sub.id} value={sub.id}>\r\n                          ID: {sub.id}\r\n                        </option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {isMultipleSelection && selectedSubmissionId && (\r\n              <div className=\"bg-primary-500 border text-neutral-100 rounded-md p-3 text-sm\">\r\n                <p className=\"font-medium\">\r\n                  Editing Submission ID: {selectedSubmissionId}\r\n                </p>\r\n                <p className=\"text-xs mt-1\">\r\n                  You are editing one submission from your multiple selections.\r\n                  Changes will only apply to this specific submission.\r\n                </p>\r\n              </div>\r\n            )}\r\n\r\n            <p className=\"text-sm text-neutral-700\">\r\n              {isMultipleSelection\r\n                ? \"You have multiple submissions selected. Choose which specific submission to edit from the dropdown above.\"\r\n                : \"You are editing a single submission. Make your changes and click Confirm & Save when done.\"}\r\n            </p>\r\n\r\n            {/* ShadCN Table */}\r\n            <div className=\"rounded-md border border-neutral-400 max-h-[450px] overflow-auto\">\r\n              <Table>\r\n                <TableHeader className=\"h-20\">\r\n                  {table.getHeaderGroups().map((headerGroup) => (\r\n                    <TableRow\r\n                      className=\"text-sm border-neutral-400\"\r\n                      key={headerGroup.id}\r\n                    >\r\n                      {headerGroup.headers.map((header) => (\r\n                        <TableHead\r\n                          className=\"py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold\"\r\n                          key={header.id}\r\n                        >\r\n                          {header.isPlaceholder\r\n                            ? null\r\n                            : flexRender(\r\n                                header.column.columnDef.header,\r\n                                header.getContext()\r\n                              )}\r\n                        </TableHead>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))}\r\n                </TableHeader>\r\n                <TableBody>\r\n                  {table.getRowModel().rows?.length ? (\r\n                    table.getRowModel().rows.map((row) => (\r\n                      <TableRow\r\n                        className=\" text-sm border-neutral-400\"\r\n                        key={row.id}\r\n                        data-state={row.getIsSelected() && \"selected\"}\r\n                      >\r\n                        {row.getVisibleCells().map((cell) => (\r\n                          <TableCell className=\"py-4 px-6\" key={cell.id}>\r\n                            {flexRender(\r\n                              cell.column.columnDef.cell,\r\n                              cell.getContext()\r\n                            )}\r\n                          </TableCell>\r\n                        ))}\r\n                      </TableRow>\r\n                    ))\r\n                  ) : (\r\n                    <TableRow>\r\n                      <TableCell\r\n                        colSpan={table.getAllColumns().length}\r\n                        className=\"h-24 text-center\"\r\n                      >\r\n                        No results.\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  )}\r\n                </TableBody>\r\n              </Table>\r\n            </div>\r\n            <div className=\"flex justify-end gap-4 mt-6\">\r\n              <button className=\"btn-outline\" onClick={onClose}>\r\n                Cancel\r\n              </button>\r\n              <button\r\n                className=\"btn-primary\"\r\n                onClick={handleSave}\r\n                disabled={updateAnswerMutation.isPending}\r\n              >\r\n                {updateAnswerMutation.isPending\r\n                  ? \"Saving...\"\r\n                  : \"Confirm & Save\"}\r\n              </button>\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <>\r\n            <h2 className=\"text-lg font-semibold text-neutral-700\">\r\n              Editing Question: {editingData?.question}\r\n            </h2>\r\n            <p className=\"text-sm text-neutral-700\">\r\n              You are about to edit responses for one or multiple submissions at\r\n              once. Use the XML syntax in the text box below. You can also\r\n              select one of the existing responses from the table of responses.\r\n              Learn more about how to edit specific responses for one or\r\n              multiple submissions\r\n            </p>\r\n            <textarea\r\n              value={newResponse}\r\n              onChange={(e) => setNewResponse(e.target.value)}\r\n              className=\"mt-4 border border-neutral-400 rounded-md p-2 w-full h-24 focus:outline-none focus:ring-2 focus:ring-primary-500\"\r\n              placeholder=\"Enter new response...\"\r\n            />\r\n            <div className=\"flex justify-end gap-4 mt-6\">\r\n              <button\r\n                className=\"btn-outline\"\r\n                onClick={() => {\r\n                  setIsEditing(false);\r\n                  setEditingData(null);\r\n                  setNewResponse(\"\");\r\n                }}\r\n                disabled={updateAnswerMutation.isPending}\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                className=\"btn-primary\"\r\n                onClick={handleSave}\r\n                disabled={updateAnswerMutation.isPending}\r\n              >\r\n                {updateAnswerMutation.isPending\r\n                  ? \"Saving...\"\r\n                  : \"Confirm & Save\"}\r\n              </button>\r\n            </div>\r\n          </>\r\n        )}\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport { EditSubmissionModal };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AASA;AAAA;AASA;AACA;AACA;AACA;AACA;AAAA;;;AA1BA;;;;;;;;;;AA8BA,8DAA8D;AAC9D,MAAM,0BAA0B,CAAC;IAC/B,IAAI,CAAC,cAAc,CAAC,WAAW,OAAO,EAAE,OAAO,EAAE;IAEjD,4CAA4C;IAC5C,MAAM,cAAc,IAAI;IAExB,WAAW,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1B,MAAM,aAAa,OAAO,QAAQ,EAAE;QACpC,MAAM,gBAAgB,OAAO,QAAQ,EAAE,SAAS;QAEhD,IAAI,CAAC,YAAY;QAEjB,IAAI,CAAC,YAAY,GAAG,CAAC,aAAa;YAChC,YAAY,GAAG,CAAC,YAAY;gBAC1B,MAAM,OAAO,QAAQ,EAAE,aAAa;gBACpC,UAAU;gBACV,gBAAgB,OAAO,QAAQ;gBAC/B,SAAS;oBAAC,OAAO,KAAK;iBAAC;gBACvB,cAAc;oBAAC;iBAAO;YACxB;QACF,OAAO;YACL,MAAM,gBAAgB,YAAY,GAAG,CAAC;YACtC,cAAc,OAAO,CAAC,IAAI,CAAC,OAAO,KAAK;YACvC,cAAc,YAAY,CAAC,IAAI,CAAC;QAClC;IACF;IAEA,uBAAuB;IACvB,OAAO,MAAM,IAAI,CAAC,YAAY,MAAM;AACtC;AAEA,MAAM,sBAAsB,CAAC,EAC3B,SAAS,EACT,OAAO,EACP,SAAS,EACT,UAAU,EACV,sBAAsB,KAAK,EAC3B,sBAAsB,EAAE,EACxB,SAAS,EASV;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE7D;IAEF,iCAAiC;IACjC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,qCAAqC;IACrC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAClD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,+BAA+B;IAC/B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,4CAA4C;IAC5C,MAAM,OAAO,6JAAA,CAAA,UAAK,CAAC,OAAO;6CAAC;YACzB,IAAI,uBAAuB,sBAAsB;gBAC/C,sEAAsE;gBACtE,MAAM,qBAAqB,oBAAoB,IAAI;4EACjD,CAAC,aAAe,WAAW,EAAE,KAAK;;gBAEpC,OAAO,qBACH,wBAAwB,sBACxB,EAAE;YACR;YACA,OAAO,wBAAwB;QACjC;4CAAG;QACD;QACA;QACA;QACA;KACD;IAED,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IACE,uBACA,oBAAoB,MAAM,GAAG,KAC7B,CAAC,sBACD;gBACA,MAAM,UAAU,mBAAmB,CAAC,EAAE,EAAE;gBACxC,IAAI,YAAY,WAAW;oBACzB,wBAAwB;gBAC1B;YACF;QACF;wCAAG;QAAC;QAAqB;QAAqB;KAAqB;IAEnE,2BAA2B;IAC3B,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,OAAO;qDAAC;YACjC,OAAO,KAAK,MAAM;6DAAC,CAAC;oBAClB,MAAM,kBACJ,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,WAAW,OAC/D,CAAC;oBACH,MAAM,gBACJ,OAAO,KAAK,OAAO,EAChB,WAAW,GACX,QAAQ,CAAC,aAAa,WAAW,OAAO,CAAC;oBAC9C,OAAO,mBAAmB;gBAC5B;;QACF;oDAAG;QAAC;QAAM;QAAgB;KAAa;IAEvC,qCAAqC;IACrC,MAAM,yBAAyB,CAC7B;QAEA,wBAAwB,OAAO,MAAM,MAAM,CAAC,KAAK;IACnD;IAEA,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN,SAAS;YACP;gBACE,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,aAAa;gBACb,MAAM;gEAAE,kBACN,6LAAC;;8CACC,6LAAC;8CAAI;;;;;;8CACL,6LAAC,6HAAA,CAAA,QAAK;oCACJ,KAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,QAAQ;oFAAE,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;;oCACjD,WAAU;;;;;;;;;;;;;YAIlB;YACA;gBACE,aAAa;gBACb,MAAM;gEAAE,kBACN,6LAAC;;8CACC,6LAAC;8CAAI;;;;;;8CACL,6LAAC,6HAAA,CAAA,QAAK;oCACJ,KAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,QAAQ;oFAAE,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;oCAC/C,WAAU;;;;;;;;;;;;;gBAIhB,IAAI;gEAAE,CAAC,EAAE,GAAG,EAAE;wBACZ,MAAM,UAAU,IAAI,QAAQ,CAAC,OAAO;wBAEpC,0DAA0D;wBAC1D,IAAI,qBAAqB;4BACvB,qBACE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAA0B;;;;;;;;;;;wBAK/C;wBAEA,qBACE,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG;4EAAC,CAAC,QAAa,sBACzB,6LAAC;wCAAgB,WAAW,QAAQ,IAAI,KAAK;kDAC1C,OAAO;uCADA;;;;;;;;;;;oBAMlB;;YACF;YACA;gBACE,aAAa;gBACb,QAAQ;gBACR,IAAI;gEAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;4BACC,WAAU;4BACV,OAAO;4EAAE,IAAM,WAAW,IAAI,QAAQ;;sCACvC;;;;;;;YAIL;SACD;QACD,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;IACjC;IAEA,MAAM,aAAa,CAAC;QAClB,aAAa;QACb,eAAe;QACf,qDAAqD;QACrD,eAAe,QAAQ,OAAO,CAAC,IAAI,CAAC;IACtC;IAEA,uCAAuC;IACvC,MAAM,uBAAuB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACvC,UAAU;qEAAE,CAAC;gBAOX,oCAAoC;gBACpC,IAAI,CAAC,KAAK,UAAU,EAAE;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,sCAAsC;gBACtC,IAAI,CAAC,KAAK,YAAY,EAAE;oBACtB,MAAM,IAAI,MAAM;gBAClB;gBAEA,uDAAuD;gBACvD,MAAM,UAMF;oBACF,cAAc,KAAK,YAAY;oBAC/B,YAAY,KAAK,UAAU;oBAC3B,YAAY,KAAK,UAAU;oBAC3B,OAAO,KAAK,KAAK;gBACnB;gBAEA,wDAAwD;gBACxD,IAAI,KAAK,UAAU,KAAK,cAAc;oBACpC,oDAAoD;oBACpD,QAAQ,gBAAgB,GAAG,MAAM,OAAO,CAAC,KAAK,gBAAgB,IAC1D,KAAK,gBAAgB,GACrB,KAAK,gBAAgB,GACrB;wBAAC,KAAK,gBAAgB;qBAAC,GACvB,EAAE,EAAE,kCAAkC;gBAC5C,OAAO,IAAI,KAAK,gBAAgB,KAAK,WAAW;oBAC9C,yDAAyD;oBACzD,QAAQ,gBAAgB,GAAG,MAAM,OAAO,CAAC,KAAK,gBAAgB,IAC1D,KAAK,gBAAgB,CAAC,EAAE,CAAC,0CAA0C;uBACnE,KAAK,gBAAgB;gBAC3B;gBAEA,oDAAoD;gBACpD,IAAI,KAAK,UAAU,KAAK,YAAY,KAAK,UAAU,KAAK,WAAW;oBACjE,uBAAuB;oBACvB,QAAQ,KAAK,GACX,OAAO,KAAK,KAAK,KAAK,WAClB,WAAW,KAAK,KAAK,IACrB,OAAO,KAAK,KAAK,KAAK,WACtB,KAAK,KAAK,GACV;gBACR,OAAO,IAAI,KAAK,UAAU,KAAK,cAAc;oBAC3C,oDAAoD;oBACpD,QAAQ,KAAK,GAAG,MAAM,OAAO,CAAC,KAAK,KAAK,IACpC,KAAK,KAAK,CAAC,GAAG;iFAAC,CAAC,IAAM,OAAO;kFAC7B;wBAAC,OAAO,KAAK,KAAK;qBAAE;gBAC1B,OAAO;oBACL,gDAAgD;oBAChD,QAAQ,KAAK,GAAG,OAAO,KAAK,KAAK;gBACnC;gBAEA,mBAAmB;gBACnB,OAAO,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,SAAS;YAC/B;;QACA,SAAS;qEAAE,CAAC;gBACV,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAGF,qCAAqC;gBACrC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAkB;gBAAC;gBAE9D,mBAAmB;gBACnB,aAAa;gBACb,eAAe;gBACf,eAAe;gBAEf,qDAAqD;gBACrD;YACF;;QACA,OAAO;qEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,0BAA0B;gBAExC,yBAAyB;gBACzB,QAAQ,KAAK,CAAC,kBAAkB;oBAC9B,UAAU,OAAO,UAAU;oBAC3B,QAAQ,OAAO,UAAU;oBACzB,SAAS,OAAO,UAAU;gBAC5B;gBAEA,mDAAmD;gBACnD,MAAM,eACJ,OAAO,UAAU,MAAM,WACvB,OAAO,UAAU,MAAM,UACvB;gBAEF,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SACE,OAAO,iBAAiB,WACpB,eACA;oBACN,MAAM;gBACR;YAEJ;;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,aAAa;QAElB,qCAAqC;QACrC,MAAM,oBACJ,uBAAuB,uBACnB,oBAAoB,IAAI,CACtB,CAAC,aAAe,WAAW,EAAE,KAAK,yBAC/B,aACL;QAEN,6BAA6B;QAC7B,IAAI,CAAC,mBAAmB,IAAI;YAC1B,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF;QACF;QAEA,8DAA8D;QAC9D,MAAM,aAAa,YAChB,KAAK,CAAC,MACN,GAAG,CAAC,CAAC,SAAW,OAAO,IAAI,IAC3B,MAAM,CAAC,CAAC,SAAW;QAEtB,qBAAqB;QACrB,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF;QACF;QAEA,MAAM,aAAa,YAAY,cAAc,EAAE;QAC/C,IAAI,CAAC,YAAY;YACf,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF;QACF;QAEA,oBAAoB;QACpB,MAAM,aAAa,YAAY,IAAI,IAAI;QAEvC,IAAI;YACF,0DAA0D;YAC1D,IAAI;YAEJ,IAAI,eAAe,cAAc;gBAC/B,iDAAiD;gBACjD,MAAM,YAAY,YAAY,YAAY,EACtC,IAAI,CAAC,OAAc,KAAK,gBAAgB,EACzC,OAAO;gBAEV,gGAAgG;gBAChG,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;oBACxC,mBAAmB,WAAW,GAAG,CAAC,IAAM;gBAC1C,OAAO,IAAI,UAAU,MAAM,KAAK,WAAW,MAAM,EAAE;oBACjD,sEAAsE;oBACtE,mBAAmB;2BAAI;qBAAU;oBACjC,MAAO,iBAAiB,MAAM,GAAG,WAAW,MAAM,CAAE;wBAClD,iBAAiB,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI;oBAC/C;gBACF,OAAO;oBACL,mBAAmB;gBACrB;YACF,OAAO;gBACL,2DAA2D;gBAC3D,mBACE,YAAY,YAAY,EAAE,CAAC,EAAE,EAAE,oBAAoB;YACvD;YAEA,4BAA4B;YAC5B,MAAM,eAAe;gBACnB,cAAc,kBAAkB,EAAE;gBAClC,YAAY;gBACZ,YAAY;gBACZ,OAAO,eAAe,eAAe,aAAa,UAAU,CAAC,EAAE;gBAC/D,kBAAkB;YACpB;YAEA,uBAAuB;YACvB,qBAAqB,MAAM,CAAC;QAC9B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;QAEJ;IACF;IAEA,qBACE;kBACE,cAAA,6LAAC,iIAAA,CAAA,UAAK;YACJ,QAAQ;YACR,SAAS;YACT,WAAU;sBAET,CAAC,0BACA;;kCACE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,sBACG,6BACA;;;;;;0CAGN,6LAAC;0CAEE,uBAAuB,oBAAoB,MAAM,GAAG,mBACnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OAAO,wBAAwB;4CAC/B,UAAU;sDAET,oBAAoB,GAAG,CAAC,CAAC,oBACxB,6LAAC;oDAAoB,OAAO,IAAI,EAAE;;wDAAE;wDAC7B,IAAI,EAAE;;mDADA,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU9B,uBAAuB,sCACtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAc;oCACD;;;;;;;0CAE1B,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAOhC,6LAAC;wBAAE,WAAU;kCACV,sBACG,8GACA;;;;;;kCAIN,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;8CACJ,6LAAC,6HAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,6LAAC,6HAAA,CAAA,WAAQ;4CACP,WAAU;sDAGT,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,6LAAC,6HAAA,CAAA,YAAS;oDACR,WAAU;8DAGT,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;mDANlB,OAAO,EAAE;;;;;2CALb,YAAY,EAAE;;;;;;;;;;8CAkBzB,6LAAC,6HAAA,CAAA,YAAS;8CACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC5B,6LAAC,6HAAA,CAAA,WAAQ;4CACP,WAAU;4CAEV,cAAY,IAAI,aAAa,MAAM;sDAElC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,6LAAC,6HAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;mDAHmB,KAAK,EAAE;;;;;2CAJ1C,IAAI,EAAE;;;;kEAcf,6LAAC,6HAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,6HAAA,CAAA,YAAS;4CACR,SAAS,MAAM,aAAa,GAAG,MAAM;4CACrC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;gCAAc,SAAS;0CAAS;;;;;;0CAGlD,6LAAC;gCACC,WAAU;gCACV,SAAS;gCACT,UAAU,qBAAqB,SAAS;0CAEvC,qBAAqB,SAAS,GAC3B,cACA;;;;;;;;;;;;;6CAKV;;kCACE,6LAAC;wBAAG,WAAU;;4BAAyC;4BAClC,aAAa;;;;;;;kCAElC,6LAAC;wBAAE,WAAU;kCAA2B;;;;;;kCAOxC,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;wBACV,aAAY;;;;;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS;oCACP,aAAa;oCACb,eAAe;oCACf,eAAe;gCACjB;gCACA,UAAU,qBAAqB,SAAS;0CACzC;;;;;;0CAGD,6LAAC;gCACC,WAAU;gCACV,SAAS;gCACT,UAAU,qBAAqB,SAAS;0CAEvC,qBAAqB,SAAS,GAC3B,cACA;;;;;;;;;;;;;;;;;;;;AAQpB;GA3jBM;;QAyBa,4JAAA,CAAA,cAAW;QACR,yLAAA,CAAA,iBAAc;QAgEpB,yLAAA,CAAA,gBAAa;QAqFE,iLAAA,CAAA,cAAW;;;KA/KpC", "debugId": null}}, {"offset": {"line": 1938, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ViewSubmissionDetail.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useRef, useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { Submission } from \"@/app/(main)/project/[hashedId]/data/table/columns\";\r\nimport { LuFullscreen } from \"react-icons/lu\";\r\n\r\nconst ViewSubmissionDetail = ({\r\n  isOpen,\r\n  onClose,\r\n  submission,\r\n}: {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  submission: Submission;\r\n}) => {\r\n  const [isFullscreen, setIsFullscreen] = useState(false);\r\n\r\n  // Ref to the container that will go fullscreen\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const handleFullscreenToggle = () => {\r\n    setIsFullscreen((prev) => !prev);\r\n  };\r\n\r\n  // Group answers by question label\r\n  const groupedAnswers = new Map<string, (string | number)[]>();\r\n  submission.answers.forEach((answer) => {\r\n    const label = answer.question.label;\r\n    if (!groupedAnswers.has(label)) {\r\n      groupedAnswers.set(label, []);\r\n    }\r\n    groupedAnswers.get(label)!.push(answer.value);\r\n  });\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      className=\"p-6 rounded-md max-w-4xl w-full \"\r\n    >\r\n      <div\r\n        ref={contentRef}\r\n        className={`flex flex-col gap-4 transition-all duration-300 ${\r\n          isFullscreen\r\n            ? \"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto\"\r\n            : \"\"\r\n        }`}\r\n      >\r\n        <div className=\"flex flex-col gap-4\">\r\n          <h2 className=\"text-lg font-semibold text-neutral-700\">\r\n            Submission Details\r\n          </h2>\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-sm text-neutral-600\">Validation status:</span>\r\n            <select className=\"px-3 py-1 border border-neutral-500 rounded-md text-sm\">\r\n              <option value=\"\">Select...</option>\r\n              <option value=\"valid\">Valid</option>\r\n              <option value=\"invalid\">Invalid</option>\r\n              <option value=\"pending\">Pending</option>\r\n            </select>\r\n            <button onClick={handleFullscreenToggle} className=\"btn-primary\">\r\n              <LuFullscreen className=\"w-5 h-5\" />\r\n              {isFullscreen ? \"Exit Fullscreen\" : \"Fullscreen\"}\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"overflow-x-auto rounded-md border border-neutral-200 bg-neutral-100\">\r\n          <table className=\"min-w-full divide-y divide-neutral-200\">\r\n            <thead className=\"bg-primary-500 text-neutral-100\">\r\n              <tr>\r\n                <th className=\"px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider\">\r\n                  Question\r\n                </th>\r\n                <th className=\"px-4 py-2 text-left text-xs font-medium uppercase tracking-wider\">\r\n                  Response\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-neutral-100 divide-y divide-neutral-200\">\r\n              {[...groupedAnswers.entries()].map(([label, values]) => {\r\n                // Find the corresponding answer to get question details\r\n                const answer = submission.answers.find(\r\n                  (a) => a.question.label === label\r\n                );\r\n                const isTableInput = answer?.question.inputType === \"table\";\r\n\r\n                return (\r\n                  <tr key={label}>\r\n                    <td className=\"px-4 py-2 align-top\">{label}</td>\r\n                    <td className=\"px-4 py-2\">\r\n                      {isTableInput ? (\r\n                        // Show simple text for table input types\r\n                        <div className=\"text-neutral-600 italic\">\r\n                          Table data (view in data table section)\r\n                        </div>\r\n                      ) : (\r\n                        // Show regular values for non-table input types\r\n                        values.join(\", \")\r\n                      )}\r\n                    </td>\r\n                  </tr>\r\n                );\r\n              })}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n\r\n        <div className=\"flex justify-between items-center mt-4\">\r\n          <div className=\"text-sm text-neutral-600 font-semibold\">\r\n            <p>Submitted by: {submission.user?.name}</p>\r\n            <p>Submission time: {submission.submissionTime}</p>\r\n          </div>\r\n          <div>\r\n            <button className=\"btn-primary\" onClick={onClose}>\r\n              Close\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default ViewSubmissionDetail;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAOA,MAAM,uBAAuB,CAAC,EAC5B,MAAM,EACN,OAAO,EACP,UAAU,EAKX;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,+CAA+C;IAC/C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,yBAAyB;QAC7B,gBAAgB,CAAC,OAAS,CAAC;IAC7B;IAEA,kCAAkC;IAClC,MAAM,iBAAiB,IAAI;IAC3B,WAAW,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1B,MAAM,QAAQ,OAAO,QAAQ,CAAC,KAAK;QACnC,IAAI,CAAC,eAAe,GAAG,CAAC,QAAQ;YAC9B,eAAe,GAAG,CAAC,OAAO,EAAE;QAC9B;QACA,eAAe,GAAG,CAAC,OAAQ,IAAI,CAAC,OAAO,KAAK;IAC9C;IAEA,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;kBAEV,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAC,gDAAgD,EAC1D,eACI,0EACA,IACJ;;8BAEF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAA2B;;;;;;8CAC3C,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;;;;;;;8CAE1B,6LAAC;oCAAO,SAAS;oCAAwB,WAAU;;sDACjD,6LAAC,iJAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,eAAe,oBAAoB;;;;;;;;;;;;;;;;;;;8BAK1C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;sDAAmE;;;;;;;;;;;;;;;;;0CAKrF,6LAAC;gCAAM,WAAU;0CACd;uCAAI,eAAe,OAAO;iCAAG,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,OAAO;oCACjD,wDAAwD;oCACxD,MAAM,SAAS,WAAW,OAAO,CAAC,IAAI,CACpC,CAAC,IAAM,EAAE,QAAQ,CAAC,KAAK,KAAK;oCAE9B,MAAM,eAAe,QAAQ,SAAS,cAAc;oCAEpD,qBACE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuB;;;;;;0DACrC,6LAAC;gDAAG,WAAU;0DACX,eACC,yCAAyC;8DACzC,6LAAC;oDAAI,WAAU;8DAA0B;;;;;2DAIzC,gDAAgD;gDAChD,OAAO,IAAI,CAAC;;;;;;;uCAVT;;;;;gCAeb;;;;;;;;;;;;;;;;;8BAKN,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAE;wCAAe,WAAW,IAAI,EAAE;;;;;;;8CACnC,6LAAC;;wCAAE;wCAAkB,WAAW,cAAc;;;;;;;;;;;;;sCAEhD,6LAAC;sCACC,cAAA,6LAAC;gCAAO,WAAU;gCAAc,SAAS;0CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;GAnHM;KAAA;uCAqHS", "debugId": null}}, {"offset": {"line": 2242, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/form-builder.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { ContextType } from \"@/types\";\r\n\r\nconst getQuestionsEndPoint = (contextType: ContextType) => {\r\n  if (contextType === \"project\") return \"/questions\";\r\n  else if (contextType === \"template\") return \"/template-questions\";\r\n  else if (contextType === \"questionBlock\") return \"/question-blocks\";\r\n  throw new Error(\"Unsupported context type\");\r\n};\r\n\r\nconst fetchQuestions = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/questions/${projectId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst fetchTemplateQuestions = async ({\r\n  templateId,\r\n}: {\r\n  templateId: number;\r\n}) => {\r\n  const { data } = await axios.get(`/template-questions/${templateId}`);\r\n  return data.questions;\r\n};\r\n\r\n// const addQuestion = async ({\r\n//   contextType,\r\n//   contextId,\r\n//   dataToSend,\r\n//   position,\r\n// }: {\r\n//   contextType: ContextType;\r\n//   contextId: number;\r\n//   dataToSend: {\r\n//     label: string;\r\n//     isRequired: boolean;\r\n//     hint?: string;\r\n//     placeholder?: string;\r\n//     inputType: string;\r\n//     questionOptions?: {\r\n//       label: string;\r\n//       code: string;\r\n//       nextQuestionId?: number | null;\r\n//     }[];\r\n//   };\r\n//   position?: number;\r\n// }) => {\r\n//   // For question blocks, we don't need to include the contextId in the URL\r\n//   // The userId is taken from the authenticated user in the backend\r\n//   const url =\r\n//     contextType === \"questionBlock\"\r\n//       ? `${getQuestionsEndPoint(contextType)}`\r\n//       : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n//   const { data } = await axios.post(url, {\r\n//     ...dataToSend,\r\n//     position: position || 1,\r\n//   });\r\n//   return data;\r\n// };\r\n\r\n// const addQuestion = async ({\r\n//   contextType,\r\n//   contextId,\r\n//   dataToSend,\r\n//   position,\r\n// }: {\r\n//   contextType: ContextType;\r\n//   contextId: number;\r\n//   dataToSend: {\r\n//     label: string;\r\n//     isRequired: boolean;\r\n//     hint?: string;\r\n//     placeholder?: string;\r\n//     inputType: string;\r\n//     questionOptions?: {\r\n//       label: string;\r\n//       code: string;\r\n//       nextQuestionId?: number | null;\r\n//     }[];\r\n//     file?: File;\r\n//   };\r\n//   position?: number;\r\n// }) => {\r\n//   // For question blocks, we don't need to include the contextId in the URL\r\n//   // The userId is taken from the authenticated user in the backend\r\n//   const url =\r\n//     contextType === \"questionBlock\"\r\n//       ? `${getQuestionsEndPoint(contextType)}`\r\n//       : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n//   // Check if we need to send form data (for file upload)\r\n//   const hasFile = dataToSend.file instanceof File;\r\n\r\n//   if (hasFile) {\r\n//     // Create FormData for file upload\r\n//     const formData = new FormData();\r\n\r\n//     // Append all the regular data\r\n//     formData.append(\"label\", dataToSend.label);\r\n//     formData.append(\"isRequired\", dataToSend.isRequired.toString());\r\n//     formData.append(\"inputType\", dataToSend.inputType);\r\n\r\n//     if (dataToSend.hint) {\r\n//       formData.append(\"hint\", dataToSend.hint);\r\n//     }\r\n\r\n//     if (dataToSend.placeholder) {\r\n//       formData.append(\"placeholder\", dataToSend.placeholder);\r\n//     }\r\n\r\n//     formData.append(\"position\", (position || 1).toString());\r\n\r\n//     // Append the Excel file\r\n//     formData.append(\"file\", dataToSend.file as File);\r\n\r\n//     const { data } = await axios.post(url, formData, {\r\n//       headers: {\r\n//         \"Content-Type\": \"multipart/form-data\",\r\n//       },\r\n//     });\r\n\r\n//     return data;\r\n//   } else {\r\n//     // Regular JSON request (no file)\r\n//     const { data } = await axios.post(url, {\r\n//       label: dataToSend.label,\r\n//       isRequired: dataToSend.isRequired,\r\n//       hint: dataToSend.hint,\r\n//       placeholder: dataToSend.placeholder,\r\n//       inputType: dataToSend.inputType,\r\n//       questionOptions: dataToSend.questionOptions,\r\n//       position: position || 1,\r\n//     });\r\n\r\n//     return data;\r\n//   }\r\n// };\r\n\r\nconst addQuestion = async ({\r\n  contextType,\r\n  contextId,\r\n  dataToSend,\r\n  position,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint?: string;\r\n    placeholder?: string;\r\n    inputType: string;\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n    file?: File;\r\n  };\r\n  position?: number;\r\n}) => {\r\n  const url =\r\n    contextType === \"questionBlock\"\r\n      ? `${getQuestionsEndPoint(contextType)}`\r\n      : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n  // Validate required fields\r\n  if (!dataToSend.label || !dataToSend.inputType) {\r\n    throw new Error(\"Label and inputType are required\");\r\n  }\r\n\r\n  // Check if this input type requires options\r\n  const needsOptions = [\"selectone\", \"selectmany\"].includes(\r\n    dataToSend.inputType\r\n  );\r\n  const hasFile = dataToSend.file instanceof File;\r\n  const hasOptions =\r\n    Array.isArray(dataToSend.questionOptions) &&\r\n    dataToSend.questionOptions.length > 0;\r\n\r\n  // Validate options based on input type and upload method\r\n  if (needsOptions && !hasFile && !hasOptions) {\r\n    throw new Error(\"Options are required for select input types\");\r\n  }\r\n\r\n  if (hasFile) {\r\n    const formData = new FormData();\r\n\r\n    // Add basic question data\r\n    formData.append(\"label\", dataToSend.label);\r\n    // Convert boolean to string in a way backend can parse\r\n    formData.append(\"isRequired\", dataToSend.isRequired ? \"true\" : \"false\");\r\n    formData.append(\"inputType\", dataToSend.inputType);\r\n    if (dataToSend.hint) formData.append(\"hint\", dataToSend.hint);\r\n    if (dataToSend.placeholder)\r\n      formData.append(\"placeholder\", dataToSend.placeholder);\r\n    // Convert number to string\r\n    formData.append(\"position\", String(position || 1));\r\n\r\n    // Add file with the correct field name\r\n    formData.append(\"file\", dataToSend.file as File);\r\n\r\n    // Important: Do NOT include questionOptions when uploading a file\r\n    // They will be parsed from the file on the server\r\n\r\n    try {\r\n      const { data } = await axios.post(url, formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"Upload error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to upload question with file: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  } else {\r\n    // Regular JSON request (no file)\r\n    try {\r\n      const { data } = await axios.post(url, {\r\n        label: dataToSend.label,\r\n        isRequired: dataToSend.isRequired,\r\n        hint: dataToSend.hint,\r\n        placeholder: dataToSend.placeholder,\r\n        inputType: dataToSend.inputType,\r\n        questionOptions: dataToSend.questionOptions,\r\n        position: position || 1,\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"API error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to add question: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  }\r\n};\r\nconst deleteQuestion = async ({\r\n  contextType,\r\n  id,\r\n  projectId,\r\n}: {\r\n  contextType: ContextType;\r\n  id: number;\r\n  projectId: number;\r\n}) => {\r\n  const { data } = await axios.delete(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`\r\n  );\r\n  return data;\r\n};\r\n\r\nconst duplicateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n}) => {\r\n  // For question blocks, we don't need to send the contextId in the body\r\n  // The userId is taken from the authenticated user in the backend\r\n  const requestBody =\r\n    contextType === \"questionBlock\"\r\n      ? {}\r\n      : contextType === \"project\"\r\n      ? { projectId: contextId }\r\n      : { templateId: contextId };\r\n\r\n  const { data } = await axios.post(\r\n    `${getQuestionsEndPoint(\r\n      contextType\r\n    )}/duplicate/${id}?projectId=${contextId}`,\r\n    requestBody\r\n  );\r\n\r\n  return data;\r\n};\r\n\r\nconst updateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  dataToSend,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint: string;\r\n    placeholder: string;\r\n    position?: number; // Optional position field to preserve question order\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n  };\r\n  contextId: number;\r\n}) => {\r\n  const { data } = await axios.patch(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`,\r\n    dataToSend\r\n  );\r\n  return data;\r\n};\r\n\r\nconst fetchQuestionBlockQuestions = async () => {\r\n  try {\r\n    const response = await axios.get(`/question-blocks`);\r\n    return response.data.questions || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching question block questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst updateQuestionPositions = async ({\r\n  contextType,\r\n  contextId,\r\n  questionPositions,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  questionPositions: { id: number; position: number }[];\r\n}) => {\r\n  // Only support position updates for projects currently\r\n  if (contextType !== \"project\") {\r\n    throw new Error(\r\n      \"Question position updates are only supported for projects\"\r\n    );\r\n  }\r\n\r\n  const url = `${getQuestionsEndPoint(\r\n    contextType\r\n  )}/positions?projectId=${contextId}`;\r\n  const payload = { questionPositions };\r\n\r\n  try {\r\n    const { data } = await axios.patch(url, payload);\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Update failed - Full error:\", error);\r\n    console.error(\"Update failed - Error details:\", {\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n      config: {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        data: error.config?.data,\r\n      },\r\n    });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport {\r\n  fetchQuestions,\r\n  fetchTemplateQuestions,\r\n  addQuestion,\r\n  deleteQuestion,\r\n  duplicateQuestion,\r\n  updateQuestion,\r\n  fetchQuestionBlockQuestions,\r\n  updateQuestionPositions,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,gBAAgB,WAAW,OAAO;SACjC,IAAI,gBAAgB,YAAY,OAAO;SACvC,IAAI,gBAAgB,iBAAiB,OAAO;IACjD,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,iBAAiB,OAAO,EAAE,SAAS,EAAyB;IAChE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;IAC1D,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,yBAAyB,OAAO,EACpC,UAAU,EAGX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY;IACpE,OAAO,KAAK,SAAS;AACvB;AAEA,+BAA+B;AAC/B,iBAAiB;AACjB,eAAe;AACf,gBAAgB;AAChB,cAAc;AACd,OAAO;AACP,8BAA8B;AAC9B,uBAAuB;AACvB,kBAAkB;AAClB,qBAAqB;AACrB,2BAA2B;AAC3B,qBAAqB;AACrB,4BAA4B;AAC5B,yBAAyB;AACzB,0BAA0B;AAC1B,uBAAuB;AACvB,sBAAsB;AACtB,wCAAwC;AACxC,WAAW;AACX,OAAO;AACP,uBAAuB;AACvB,UAAU;AACV,8EAA8E;AAC9E,sEAAsE;AACtE,gBAAgB;AAChB,sCAAsC;AACtC,iDAAiD;AACjD,+DAA+D;AAE/D,6CAA6C;AAC7C,qBAAqB;AACrB,+BAA+B;AAC/B,QAAQ;AACR,iBAAiB;AACjB,KAAK;AAEL,+BAA+B;AAC/B,iBAAiB;AACjB,eAAe;AACf,gBAAgB;AAChB,cAAc;AACd,OAAO;AACP,8BAA8B;AAC9B,uBAAuB;AACvB,kBAAkB;AAClB,qBAAqB;AACrB,2BAA2B;AAC3B,qBAAqB;AACrB,4BAA4B;AAC5B,yBAAyB;AACzB,0BAA0B;AAC1B,uBAAuB;AACvB,sBAAsB;AACtB,wCAAwC;AACxC,WAAW;AACX,mBAAmB;AACnB,OAAO;AACP,uBAAuB;AACvB,UAAU;AACV,8EAA8E;AAC9E,sEAAsE;AACtE,gBAAgB;AAChB,sCAAsC;AACtC,iDAAiD;AACjD,+DAA+D;AAE/D,4DAA4D;AAC5D,qDAAqD;AAErD,mBAAmB;AACnB,yCAAyC;AACzC,uCAAuC;AAEvC,qCAAqC;AACrC,kDAAkD;AAClD,uEAAuE;AACvE,0DAA0D;AAE1D,6BAA6B;AAC7B,kDAAkD;AAClD,QAAQ;AAER,oCAAoC;AACpC,gEAAgE;AAChE,QAAQ;AAER,+DAA+D;AAE/D,+BAA+B;AAC/B,wDAAwD;AAExD,yDAAyD;AACzD,mBAAmB;AACnB,iDAAiD;AACjD,WAAW;AACX,UAAU;AAEV,mBAAmB;AACnB,aAAa;AACb,wCAAwC;AACxC,+CAA+C;AAC/C,iCAAiC;AACjC,2CAA2C;AAC3C,+BAA+B;AAC/B,6CAA6C;AAC7C,yCAAyC;AACzC,qDAAqD;AACrD,iCAAiC;AACjC,UAAU;AAEV,mBAAmB;AACnB,MAAM;AACN,KAAK;AAEL,MAAM,cAAc,OAAO,EACzB,WAAW,EACX,SAAS,EACT,UAAU,EACV,QAAQ,EAmBT;IACC,MAAM,MACJ,gBAAgB,kBACZ,GAAG,qBAAqB,cAAc,GACtC,GAAG,qBAAqB,aAAa,CAAC,EAAE,WAAW;IAEzD,2BAA2B;IAC3B,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,SAAS,EAAE;QAC9C,MAAM,IAAI,MAAM;IAClB;IAEA,4CAA4C;IAC5C,MAAM,eAAe;QAAC;QAAa;KAAa,CAAC,QAAQ,CACvD,WAAW,SAAS;IAEtB,MAAM,UAAU,WAAW,IAAI,YAAY;IAC3C,MAAM,aACJ,MAAM,OAAO,CAAC,WAAW,eAAe,KACxC,WAAW,eAAe,CAAC,MAAM,GAAG;IAEtC,yDAAyD;IACzD,IAAI,gBAAgB,CAAC,WAAW,CAAC,YAAY;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,SAAS;QACX,MAAM,WAAW,IAAI;QAErB,0BAA0B;QAC1B,SAAS,MAAM,CAAC,SAAS,WAAW,KAAK;QACzC,uDAAuD;QACvD,SAAS,MAAM,CAAC,cAAc,WAAW,UAAU,GAAG,SAAS;QAC/D,SAAS,MAAM,CAAC,aAAa,WAAW,SAAS;QACjD,IAAI,WAAW,IAAI,EAAE,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAC5D,IAAI,WAAW,WAAW,EACxB,SAAS,MAAM,CAAC,eAAe,WAAW,WAAW;QACvD,2BAA2B;QAC3B,SAAS,MAAM,CAAC,YAAY,OAAO,YAAY;QAE/C,uCAAuC;QACvC,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAEvC,kEAAkE;QAClE,kDAAkD;QAElD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK,UAAU;gBAC/C,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,yBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,qCAAqC,EACpC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF,OAAO;QACL,iCAAiC;QACjC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK;gBACrC,OAAO,WAAW,KAAK;gBACvB,YAAY,WAAW,UAAU;gBACjC,MAAM,WAAW,IAAI;gBACrB,aAAa,WAAW,WAAW;gBACnC,WAAW,WAAW,SAAS;gBAC/B,iBAAiB,WAAW,eAAe;gBAC3C,UAAU,YAAY;YACxB;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,sBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,wBAAwB,EACvB,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF;AACF;AACA,MAAM,iBAAiB,OAAO,EAC5B,WAAW,EACX,EAAE,EACF,SAAS,EAKV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CACjC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW;IAErE,OAAO;AACT;AAEA,MAAM,oBAAoB,OAAO,EAC/B,EAAE,EACF,WAAW,EACX,SAAS,EAKV;IACC,uEAAuE;IACvE,iEAAiE;IACjE,MAAM,cACJ,gBAAgB,kBACZ,CAAC,IACD,gBAAgB,YAChB;QAAE,WAAW;IAAU,IACvB;QAAE,YAAY;IAAU;IAE9B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,qBACD,aACA,WAAW,EAAE,GAAG,WAAW,EAAE,WAAW,EAC1C;IAGF,OAAO;AACT;AAEA,MAAM,iBAAiB,OAAO,EAC5B,EAAE,EACF,WAAW,EACX,UAAU,EACV,SAAS,EAkBV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW,EACnE;IAEF,OAAO;AACT;AAEA,MAAM,8BAA8B;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC;QACnD,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAEA,MAAM,0BAA0B,OAAO,EACrC,WAAW,EACX,SAAS,EACT,iBAAiB,EAKlB;IACC,uDAAuD;IACvD,IAAI,gBAAgB,WAAW;QAC7B,MAAM,IAAI,MACR;IAEJ;IAEA,MAAM,MAAM,GAAG,qBACb,aACA,qBAAqB,EAAE,WAAW;IACpC,MAAM,UAAU;QAAE;IAAkB;IAEpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,KAAK;QACxC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,QAAQ,KAAK,CAAC,kCAAkC;YAC9C,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,MAAM,MAAM,QAAQ,EAAE;YACtB,SAAS,MAAM,OAAO;YACtB,QAAQ;gBACN,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,MAAM,MAAM,MAAM,EAAE;YACtB;QACF;QACA,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 2497, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ConfirmationModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Modal from \"./Modal\";\r\n\r\nconst ConfirmationModal = ({\r\n  showModal,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  description,\r\n  confirmButtonText,\r\n  cancelButtonText,\r\n  confirmButtonClass,\r\n  children,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  description: React.ReactNode; // Accept ReactNode for flexible content\r\n  confirmButtonText: string;\r\n  cancelButtonText?: string;\r\n  confirmButtonClass?: string;\r\n  children?: React.ReactNode; // Additional content like warnings or icons\r\n}) => {\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={onClose}\r\n      className=\"p-6 rounded-md max-w-xl\"\r\n    >\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">{title}</h2>\r\n      <div className=\"text-neutral-700 mt-2\">{description}</div>\r\n      {children && <div className=\"mt-6 space-y-4\">{children}</div>}\r\n      <div className=\"flex justify-end gap-4 mt-6\">\r\n        <button className=\"btn-outline\" onClick={onClose} type=\"button\">\r\n          {cancelButtonText || \"Cancel\"}\r\n        </button>\r\n        <button\r\n          className={`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${confirmButtonClass}`}\r\n          onClick={onConfirm}\r\n          type=\"button\"\r\n        >\r\n          {confirmButtonText}\r\n        </button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ConfirmationModal };\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,EAWT;IACC,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,6LAAC;gBAAI,WAAU;0BAAyB;;;;;;YACvC,0BAAY,6LAAC;gBAAI,WAAU;0BAAkB;;;;;;0BAC9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;wBAAc,SAAS;wBAAS,MAAK;kCACpD,oBAAoB;;;;;;kCAEvB,6LAAC;wBACC,WAAW,CAAC,+IAA+I,EAAE,oBAAoB;wBACjL,SAAS;wBACT,MAAK;kCAEJ;;;;;;;;;;;;;;;;;;AAKX;KA5CM", "debugId": null}}, {"offset": {"line": 2584, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%28main%29/project/%5BhashedId%5D/data/table/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { generateColumns, Submission } from \"./columns\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport React, { JSX, useState, useRef, useEffect } from \"react\";\r\nimport { BiSolidEdit } from \"react-icons/bi\";\r\nimport { RiDeleteBin6Fill } from \"react-icons/ri\";\r\nimport { FaChevronDown, FaChevronUp } from \"react-icons/fa\";\r\nimport { LuFullscreen } from \"react-icons/lu\";\r\nimport { EditSubmissionModal } from \"@/components/modals/EditSubmissionModal\";\r\nimport { GeneralTable } from \"@/components/tables/GeneralTable\";\r\nimport ViewSubmissionDetail from \"@/components/modals/ViewSubmissionDetail\";\r\nimport { fetchQuestions } from \"@/lib/api/form-builder\";\r\nimport { Question } from \"@/types/formBuilder\";\r\n\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\nimport {\r\n  Table as ReactTable,\r\n  RowSelectionState,\r\n  VisibilityState,\r\n} from \"@tanstack/react-table\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ConfirmationModal } from \"@/components/modals/ConfirmationModal\";\r\nimport axios from \"@/lib/axios\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport {\r\n  deleteFormSubmission,\r\n  deleteMultipleFormSubmissions,\r\n} from \"@/lib/api/submission\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\n\r\ntype ConfirmationModalProps = {\r\n  title: string;\r\n  description: string | JSX.Element;\r\n  confirmButtonText: string;\r\n  confirmButtonClass: string;\r\n  onConfirm: () => void;\r\n} | null;\r\n\r\nconst fetchSubmissions = async (projectId: number) => {\r\n  const { data } = await axios.get(`/form-submissions/${projectId}`);\r\n  return data.data.formSubmissions;\r\n};\r\n\r\nconst STORAGE_KEY = \"data-table-column-visibility\";\r\n\r\nconst Page = () => {\r\n  const { hashedId } = useParams();\r\n  const hashedIdString = hashedId as string;\r\n\r\n  const projectId = Number(decode(hashedIdString));\r\n  const dispatch = useDispatch();\r\n  const queryClient = useQueryClient();\r\n\r\n  const {\r\n    data: submissions = [],\r\n    isLoading,\r\n    refetch,\r\n  } = useQuery<Submission[]>({\r\n    queryKey: [\"formSubmissions\", projectId],\r\n    queryFn: () => fetchSubmissions(projectId!),\r\n    enabled: projectId !== null,\r\n    refetchInterval: 1000,\r\n    staleTime: 0, // Always consider data stale to ensure fresh fetches\r\n    gcTime: 0, // Don't cache the data (updated from cacheTime)\r\n  });\r\n\r\n  // Fetch ALL questions for the project (including conditional questions)\r\n  const { data: allQuestions = [], isLoading: isLoadingQuestions } = useQuery<\r\n    Question[]\r\n  >({\r\n    queryKey: [\"allQuestions\", projectId],\r\n    queryFn: () => fetchQuestions({ projectId: projectId! }),\r\n    enabled: projectId !== null,\r\n    staleTime: 5 * 60 * 1000, // Cache for 5 minutes since questions don't change often\r\n  });\r\n\r\n  const [isFullscreen, setIsFullscreen] = useState(false);\r\n  const [showConfirmationModal, setShowConfirmationModal] = useState(false);\r\n  const [globalFilter, setGlobalFilter] = useState(\"\");\r\n  const [isAnyRowSelected, setIsAnyRowSelected] = useState(false);\r\n\r\n  const [confirmationModalContent, setConfirmationModalContent] =\r\n    React.useState<ConfirmationModalProps>(null);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [columnVisibility, setColumnVisibility] =\r\n    React.useState<VisibilityState>({});\r\n  const [tableInstance, setTableInstance] =\r\n    React.useState<ReactTable<Submission> | null>(null);\r\n\r\n  const [selectedRows, setSelectedRows] = React.useState<RowSelectionState>({});\r\n\r\n  const [showViewModal, setShowViewModal] = useState(false);\r\n  const [selectedSubmission, setSelectedSubmission] =\r\n    useState<Submission | null>(null);\r\n\r\n  const handleViewSubmission = (submission: Submission) => {\r\n    setSelectedSubmission(submission);\r\n    setShowViewModal(true);\r\n  };\r\n\r\n  const columns =\r\n    submissions.length > 0 && allQuestions.length > 0\r\n      ? generateColumns(\r\n          handleViewSubmission,\r\n          submissions[0],\r\n          hashedIdString,\r\n          allQuestions\r\n        )\r\n      : [];\r\n\r\n  const [isDropdownOpen, setIsDropdownOpen] = React.useState(false);\r\n  const [statusDropdownOpen, setStatusDropdownOpen] = useState(false);\r\n\r\n  const statusDropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Ref to the container that will go fullscreen\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Callback to monitor row selection changes\r\n  const handleRowSelectionChange = (rowSelection: Record<string, boolean>) => {\r\n    setIsAnyRowSelected(Object.keys(rowSelection).length > 0);\r\n    setSelectedRows(rowSelection);\r\n\r\n    // Get the selected submission when row selection changes\r\n    if (Object.keys(rowSelection).length === 1) {\r\n      // If exactly one row is selected\r\n      const selectedIndex = Number(Object.keys(rowSelection)[0]);\r\n      setSelectedSubmission(submissions[selectedIndex]);\r\n    } else if (Object.keys(rowSelection).length === 0) {\r\n      // If no rows are selected\r\n      setSelectedSubmission(null);\r\n    }\r\n  };\r\n\r\n  const handleFullscreenToggle = () => {\r\n    setIsFullscreen((prev) => !prev);\r\n  };\r\n\r\n  // Handle status dropdown toggling\r\n  const handleStatusDropdownToggle = () => {\r\n    setStatusDropdownOpen((prev) => !prev);\r\n  };\r\n\r\n  const handleEditConfirm = () => {\r\n    // You would typically save the updated submission to the backend here\r\n  \r\n\r\n    // Refresh the data using invalidateQueries\r\n    queryClient.invalidateQueries({ queryKey: [\"formSubmissions\", projectId] });\r\n\r\n    // Clear row selections\r\n    setSelectedRows({});\r\n    setIsAnyRowSelected(false);\r\n\r\n    // Close the modal\r\n    setShowEditModal(false);\r\n    setSelectedSubmission(null);\r\n  };\r\n\r\n  // Mutation for deleting a single submission\r\n  const deleteMutation = useMutation({\r\n    mutationFn: (submissionId: number) =>\r\n      deleteFormSubmission(submissionId, projectId),\r\n    onSuccess: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Submission deleted successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      queryClient.invalidateQueries({\r\n        queryKey: [\"formSubmissions\", projectId],\r\n      });\r\n\r\n      // Clear selections\r\n      setSelectedRows({});\r\n      setIsAnyRowSelected(false);\r\n      setSelectedSubmission(null);\r\n      setShowConfirmationModal(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error deleting submission:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to delete submission\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  // Mutation for deleting multiple submissions\r\n  const deleteMultipleMutation = useMutation({\r\n    mutationFn: (submissionIds: number[]) =>\r\n      deleteMultipleFormSubmissions(submissionIds, projectId),\r\n    onSuccess: (_, variables) => {\r\n      dispatch(\r\n        showNotification({\r\n          message: `${variables.length} submissions deleted successfully`,\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      // Refresh the data\r\n      queryClient.invalidateQueries({\r\n        queryKey: [\"formSubmissions\", projectId],\r\n      });\r\n\r\n      // Clear selections\r\n      setSelectedRows({});\r\n      setIsAnyRowSelected(false);\r\n      setSelectedSubmission(null);\r\n      setShowConfirmationModal(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error deleting multiple submissions:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Failed to delete submissions\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setShowConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  const handleDeleteClick = () => {\r\n    // Get the IDs of the selected submissions\r\n    const selectedSubmissionIds = Object.keys(selectedRows)\r\n      .map((key) => {\r\n        const index = parseInt(key);\r\n        return submissions[index]?.id || 0;\r\n      })\r\n      .filter((id) => id > 0);\r\n\r\n    setConfirmationModalContent({\r\n      title: \"Confirm Deletion\",\r\n      description: (\r\n        <>\r\n          <p>\r\n            Are you sure you want to delete{\" \"}\r\n            {selectedSubmissionIds.length > 1\r\n              ? `these ${selectedSubmissionIds.length} submissions`\r\n              : \"this submission\"}\r\n            ? It is not possible to recover deleted submissions.\r\n          </p>\r\n        </>\r\n      ),\r\n      confirmButtonText: \"Delete\",\r\n      confirmButtonClass: \"bg-red-500 hover:bg-red-600 cursor-pointer\",\r\n      onConfirm: () => {\r\n        if (selectedSubmissionIds.length === 1) {\r\n          // Delete a single submission using the mutation\r\n          deleteMutation.mutate(selectedSubmissionIds[0]);\r\n        } else if (selectedSubmissionIds.length > 1) {\r\n          // Delete multiple submissions using the mutation\r\n          deleteMultipleMutation.mutate(selectedSubmissionIds);\r\n        }\r\n      },\r\n    });\r\n    setShowConfirmationModal(true);\r\n  };\r\n\r\n  const handleEditClick = () => {\r\n    const selectedIds = Object.keys(selectedRows);\r\n\r\n    if (selectedIds.length === 0) {\r\n      dispatch(\r\n        showNotification({\r\n          message: \"No submission selected for editing\",\r\n          type: \"error\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Collect all selected submissions\r\n    const selectedSubmissionsArray = selectedIds\r\n      .map((id) => {\r\n        const index = Number(id);\r\n        return submissions[index];\r\n      })\r\n      .filter(Boolean);\r\n\r\n    // If exactly one row is selected, use that submission\r\n    if (selectedIds.length === 1) {\r\n      setSelectedSubmission(selectedSubmissionsArray[0]);\r\n      setShowEditModal(true);\r\n      return;\r\n    }\r\n\r\n    // For multiple selections, we'll set the first one as the default\r\n    if (selectedIds.length > 1) {\r\n      setSelectedSubmission(selectedSubmissionsArray[0]);\r\n      setShowEditModal(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (\r\n        statusDropdownRef.current &&\r\n        !statusDropdownRef.current.contains(event.target as Node)\r\n      ) {\r\n        setStatusDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    try {\r\n      const saved = localStorage.getItem(STORAGE_KEY);\r\n      if (saved) {\r\n        const parsed = JSON.parse(saved);\r\n        if (parsed && typeof parsed === \"object\" && !Array.isArray(parsed)) {\r\n          setColumnVisibility(parsed);\r\n        } else {\r\n          console.warn(\"Invalid format in localstorage for column visibility\");\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading column visibility:\", error);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (Object.keys(columnVisibility).length > 0) {\r\n      try {\r\n        localStorage.setItem(STORAGE_KEY, JSON.stringify(columnVisibility));\r\n      } catch (error) {\r\n        console.error(\"Error saving column visibility:\", error);\r\n      }\r\n    }\r\n  }, [columnVisibility]);\r\n\r\n  // Effect to properly update the table when selectedRows changes\r\n  useEffect(() => {\r\n    if (tableInstance && Object.keys(selectedRows).length === 0) {\r\n      // If selectedRows is empty and we have a table instance,\r\n      // explicitly reset the table's row selection\r\n      tableInstance.resetRowSelection();\r\n    }\r\n  }, [selectedRows, tableInstance]);\r\n\r\n  const handleColumnVisibilityChange = (newState: VisibilityState) => {\r\n    setColumnVisibility(newState);\r\n  };\r\n\r\n  const handleTableInit = (table: ReactTable<Submission>) => {\r\n    setTableInstance(table);\r\n\r\n    if (Object.keys(columnVisibility).length > 0) {\r\n      table.setColumnVisibility(columnVisibility);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={contentRef}\r\n      className={`flex flex-col gap-4 transition-all duration-300 ${\r\n        isFullscreen\r\n          ? \"fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto\"\r\n          : \"\"\r\n      }`}\r\n    >\r\n      <div className=\"flex flex-col desktop:flex-row justify-between gap-8 items-center py-4\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <Input\r\n            placeholder=\"Search all columns...\"\r\n            value={globalFilter}\r\n            onChange={(e) => setGlobalFilter(e.target.value)}\r\n          />\r\n\r\n          {tableInstance && (\r\n            <DropdownMenu\r\n              open={isDropdownOpen}\r\n              onOpenChange={(open) => setIsDropdownOpen(open)}\r\n            >\r\n              <DropdownMenuTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"flex items-center gap-2 cursor-pointer\"\r\n                >\r\n                  Show/Hide Columns\r\n                  {isDropdownOpen ? (\r\n                    <FaChevronUp className=\"w-3 h-3\" />\r\n                  ) : (\r\n                    <FaChevronDown className=\"w-3 h-3\" />\r\n                  )}\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent\r\n                align=\"start\"\r\n                className=\"bg-neutral-100 border border-neutral-200 shadow-md\"\r\n              >\r\n                {tableInstance\r\n                  .getAllColumns()\r\n                  .filter((column) => column.getCanHide())\r\n                  .map((column) => (\r\n                    <DropdownMenuCheckboxItem\r\n                      key={column.id}\r\n                      className=\"capitalize cursor-pointer hover:bg-neutral-200\"\r\n                      checked={columnVisibility[column.id] ?? true}\r\n                      onCheckedChange={(value) =>\r\n                        setColumnVisibility((prev) => ({\r\n                          ...prev,\r\n                          [column.id]: value,\r\n                        }))\r\n                      }\r\n                    >\r\n                      {column.id}\r\n                    </DropdownMenuCheckboxItem>\r\n                  ))}\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          )}\r\n        </div>\r\n        <div\r\n          ref={statusDropdownRef}\r\n          className=\"flex relative items-center gap-4 text-neutral-800\"\r\n        >\r\n          <button onClick={handleFullscreenToggle} className=\"btn-primary\">\r\n            <LuFullscreen className=\"w-5 h-5\" />\r\n            {isFullscreen ? \"Exit Fullscreen\" : \"Fullscreen\"}\r\n          </button>\r\n\r\n          <button\r\n            className={` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${\r\n              isAnyRowSelected\r\n                ? \"hover:bg-primary-600  cursor-pointer\"\r\n                : \"opacity-50\"\r\n            }`}\r\n            onClick={isAnyRowSelected ? handleStatusDropdownToggle : undefined}\r\n          >\r\n            Status\r\n            {statusDropdownOpen ? (\r\n              <FaChevronUp className=\"w-3 h-3\" />\r\n            ) : (\r\n              <FaChevronDown className=\"w-3 h-3\" />\r\n            )}\r\n          </button>\r\n          {statusDropdownOpen && (\r\n            <div className=\"absolute left-30 top-10 mt-2 w-64 bg-neutral-100 border border-gray-200 shadow-md rounded-md p-2 z-40\">\r\n              <div className=\"flex flex-col  gap-2\">\r\n                <div className=\"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm\">\r\n                  Set on: Approved\r\n                </div>\r\n                <div className=\"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm\">\r\n                  Set on: Not Approved\r\n                </div>\r\n                <div className=\"hover:bg-neutral-200 cursor-pointer p-2 text-neutral-800 rounded-sm\">\r\n                  Set on: On Hold\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          <button\r\n            className={` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${\r\n              isAnyRowSelected\r\n                ? \"hover:bg-primary-600  cursor-pointer\"\r\n                : \"opacity-50\"\r\n            }`}\r\n            onClick={isAnyRowSelected ? handleEditClick : undefined}\r\n          >\r\n            <BiSolidEdit className=\"h-4 w-4\" />\r\n            Edit\r\n          </button>\r\n          <button\r\n            className={` bg-primary-500 font-medium rounded-lg shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2  active:scale-95 transition-all duration-300 ${\r\n              isAnyRowSelected\r\n                ? \"hover:bg-primary-600  cursor-pointer\"\r\n                : \"opacity-50\"\r\n            }`}\r\n            onClick={isAnyRowSelected ? handleDeleteClick : undefined}\r\n          >\r\n            <RiDeleteBin6Fill className=\"h-4 w-4\" />\r\n            Delete\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {isLoading || isLoadingQuestions ? (\r\n        <div className=\"flex justify-center items-center py-12\">\r\n          <div className=\"text-muted-foreground\">Loading data...</div>\r\n        </div>\r\n      ) : (\r\n        <GeneralTable\r\n          columns={columns}\r\n          data={submissions}\r\n          globalFilter={globalFilter}\r\n          setGlobalFilter={setGlobalFilter}\r\n          onTableInit={handleTableInit}\r\n          columnVisibility={columnVisibility}\r\n          setColumnVisibility={handleColumnVisibilityChange}\r\n          onRowSelectionChange={handleRowSelectionChange}\r\n          rowSelection={selectedRows}\r\n        />\r\n      )}\r\n\r\n      {/* Conditional rendering of the EditSubmissionModal */}\r\n      {showEditModal && selectedSubmission && (\r\n        <EditSubmissionModal\r\n          showModal={showEditModal}\r\n          projectId={projectId}\r\n          onClose={() => {\r\n            setShowEditModal(false);\r\n            setSelectedSubmission(null);\r\n            // Clear row selections when closing the modal\r\n            setSelectedRows({});\r\n            setIsAnyRowSelected(false);\r\n          }}\r\n          onConfirm={handleEditConfirm}\r\n          submission={selectedSubmission}\r\n          isMultipleSelection={Object.keys(selectedRows).length > 1}\r\n          selectedSubmissions={\r\n            Object.keys(selectedRows).length > 1\r\n              ? Object.keys(selectedRows)\r\n                  .map((id) => submissions[Number(id)])\r\n                  .filter(Boolean)\r\n              : []\r\n          }\r\n        />\r\n      )}\r\n\r\n      {confirmationModalContent && (\r\n        <ConfirmationModal\r\n          showModal={showConfirmationModal}\r\n          onClose={() => setShowConfirmationModal(false)}\r\n          onConfirm={confirmationModalContent.onConfirm}\r\n          title={confirmationModalContent.title}\r\n          description={confirmationModalContent.description}\r\n          confirmButtonText={confirmationModalContent.confirmButtonText}\r\n          confirmButtonClass={confirmationModalContent.confirmButtonClass}\r\n        />\r\n      )}\r\n\r\n      {selectedSubmission && (\r\n        <ViewSubmissionDetail\r\n          isOpen={showViewModal}\r\n          onClose={() => setShowViewModal(false)}\r\n          submission={selectedSubmission}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Page;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAYA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAIA;AACA;;;AAtCA;;;;;;;;;;;;;;;;;;;;;;AAgDA,MAAM,mBAAmB,OAAO;IAC9B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW;IACjE,OAAO,KAAK,IAAI,CAAC,eAAe;AAClC;AAEA,MAAM,cAAc;AAEpB,MAAM,OAAO;;IACX,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,iBAAiB;IAEvB,MAAM,YAAY,OAAO,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,EACJ,MAAM,cAAc,EAAE,EACtB,SAAS,EACT,OAAO,EACR,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAgB;QACzB,UAAU;YAAC;YAAmB;SAAU;QACxC,OAAO;6BAAE,IAAM,iBAAiB;;QAChC,SAAS,cAAc;QACvB,iBAAiB;QACjB,WAAW;QACX,QAAQ;IACV;IAEA,wEAAwE;IACxE,MAAM,EAAE,MAAM,eAAe,EAAE,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAExE;QACA,UAAU;YAAC;YAAgB;SAAU;QACrC,OAAO;6BAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QACtD,SAAS,cAAc;QACvB,WAAW,IAAI,KAAK;IACtB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,CAAC,0BAA0B,4BAA4B,GAC3D,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAyB;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAkB,CAAC;IACnC,MAAM,CAAC,eAAe,iBAAiB,GACrC,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAgC;IAEhD,MAAM,CAAC,cAAc,gBAAgB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAoB,CAAC;IAE3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAE9B,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,iBAAiB;IACnB;IAEA,MAAM,UACJ,YAAY,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,IAC5C,CAAA,GAAA,0KAAA,CAAA,kBAAe,AAAD,EACZ,sBACA,WAAW,CAAC,EAAE,EACd,gBACA,gBAEF,EAAE;IAER,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEjD,+CAA+C;IAC/C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,4CAA4C;IAC5C,MAAM,2BAA2B,CAAC;QAChC,oBAAoB,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG;QACvD,gBAAgB;QAEhB,yDAAyD;QACzD,IAAI,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK,GAAG;YAC1C,iCAAiC;YACjC,MAAM,gBAAgB,OAAO,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE;YACzD,sBAAsB,WAAW,CAAC,cAAc;QAClD,OAAO,IAAI,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK,GAAG;YACjD,0BAA0B;YAC1B,sBAAsB;QACxB;IACF;IAEA,MAAM,yBAAyB;QAC7B,gBAAgB,CAAC,OAAS,CAAC;IAC7B;IAEA,kCAAkC;IAClC,MAAM,6BAA6B;QACjC,sBAAsB,CAAC,OAAS,CAAC;IACnC;IAEA,MAAM,oBAAoB;QACxB,sEAAsE;QAGtE,2CAA2C;QAC3C,YAAY,iBAAiB,CAAC;YAAE,UAAU;gBAAC;gBAAmB;aAAU;QAAC;QAEzE,uBAAuB;QACvB,gBAAgB,CAAC;QACjB,oBAAoB;QAEpB,kBAAkB;QAClB,iBAAiB;QACjB,sBAAsB;IACxB;IAEA,4CAA4C;IAC5C,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjC,UAAU;gDAAE,CAAC,eACX,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD,EAAE,cAAc;;QACrC,SAAS;gDAAE;gBACT,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAGF,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAmB;qBAAU;gBAC1C;gBAEA,mBAAmB;gBACnB,gBAAgB,CAAC;gBACjB,oBAAoB;gBACpB,sBAAsB;gBACtB,yBAAyB;YAC3B;;QACA,OAAO;gDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,yBAAyB;YAC3B;;IACF;IAEA,6CAA6C;IAC7C,MAAM,yBAAyB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACzC,UAAU;wDAAE,CAAC,gBACX,CAAA,GAAA,2HAAA,CAAA,gCAA6B,AAAD,EAAE,eAAe;;QAC/C,SAAS;wDAAE,CAAC,GAAG;gBACb,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,GAAG,UAAU,MAAM,CAAC,iCAAiC,CAAC;oBAC/D,MAAM;gBACR;gBAGF,mBAAmB;gBACnB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAmB;qBAAU;gBAC1C;gBAEA,mBAAmB;gBACnB,gBAAgB,CAAC;gBACjB,oBAAoB;gBACpB,sBAAsB;gBACtB,yBAAyB;YAC3B;;QACA,OAAO;wDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;gBAEF,yBAAyB;YAC3B;;IACF;IAEA,MAAM,oBAAoB;QACxB,0CAA0C;QAC1C,MAAM,wBAAwB,OAAO,IAAI,CAAC,cACvC,GAAG,CAAC,CAAC;YACJ,MAAM,QAAQ,SAAS;YACvB,OAAO,WAAW,CAAC,MAAM,EAAE,MAAM;QACnC,GACC,MAAM,CAAC,CAAC,KAAO,KAAK;QAEvB,4BAA4B;YAC1B,OAAO;YACP,2BACE;0BACE,cAAA,6LAAC;;wBAAE;wBAC+B;wBAC/B,sBAAsB,MAAM,GAAG,IAC5B,CAAC,MAAM,EAAE,sBAAsB,MAAM,CAAC,YAAY,CAAC,GACnD;wBAAkB;;;;;;;;YAK5B,mBAAmB;YACnB,oBAAoB;YACpB,WAAW;gBACT,IAAI,sBAAsB,MAAM,KAAK,GAAG;oBACtC,gDAAgD;oBAChD,eAAe,MAAM,CAAC,qBAAqB,CAAC,EAAE;gBAChD,OAAO,IAAI,sBAAsB,MAAM,GAAG,GAAG;oBAC3C,iDAAiD;oBACjD,uBAAuB,MAAM,CAAC;gBAChC;YACF;QACF;QACA,yBAAyB;IAC3B;IAEA,MAAM,kBAAkB;QACtB,MAAM,cAAc,OAAO,IAAI,CAAC;QAEhC,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS;gBACT,MAAM;YACR;YAEF;QACF;QAEA,mCAAmC;QACnC,MAAM,2BAA2B,YAC9B,GAAG,CAAC,CAAC;YACJ,MAAM,QAAQ,OAAO;YACrB,OAAO,WAAW,CAAC,MAAM;QAC3B,GACC,MAAM,CAAC;QAEV,sDAAsD;QACtD,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,sBAAsB,wBAAwB,CAAC,EAAE;YACjD,iBAAiB;YACjB;QACF;QAEA,kEAAkE;QAClE,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,sBAAsB,wBAAwB,CAAC,EAAE;YACjD,iBAAiB;QACnB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;qDAAqB,CAAC;oBAC1B,IACE,kBAAkB,OAAO,IACzB,CAAC,kBAAkB,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAChD;wBACA,sBAAsB;oBACxB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;kCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;yBAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,IAAI,UAAU,OAAO,WAAW,YAAY,CAAC,MAAM,OAAO,CAAC,SAAS;wBAClE,oBAAoB;oBACtB,OAAO;wBACL,QAAQ,IAAI,CAAC;oBACf;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;yBAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,GAAG;gBAC5C,IAAI;oBACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBACnD,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACnD;YACF;QACF;yBAAG;QAAC;KAAiB;IAErB,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,iBAAiB,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK,GAAG;gBAC3D,yDAAyD;gBACzD,6CAA6C;gBAC7C,cAAc,iBAAiB;YACjC;QACF;yBAAG;QAAC;QAAc;KAAc;IAEhC,MAAM,+BAA+B,CAAC;QACpC,oBAAoB;IACtB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QAEjB,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,GAAG;YAC5C,MAAM,mBAAmB,CAAC;QAC5B;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAC,gDAAgD,EAC1D,eACI,0EACA,IACJ;;0BAEF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;4BAGhD,+BACC,6LAAC,wIAAA,CAAA,eAAY;gCACX,MAAM;gCACN,cAAc,CAAC,OAAS,kBAAkB;;kDAE1C,6LAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;;gDACX;gDAEE,+BACC,6LAAC,iJAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,6LAAC,iJAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAI/B,6LAAC,wIAAA,CAAA,sBAAmB;wCAClB,OAAM;wCACN,WAAU;kDAET,cACE,aAAa,GACb,MAAM,CAAC,CAAC,SAAW,OAAO,UAAU,IACpC,GAAG,CAAC,CAAC,uBACJ,6LAAC,wIAAA,CAAA,2BAAwB;gDAEvB,WAAU;gDACV,SAAS,gBAAgB,CAAC,OAAO,EAAE,CAAC,IAAI;gDACxC,iBAAiB,CAAC,QAChB,oBAAoB,CAAC,OAAS,CAAC;4DAC7B,GAAG,IAAI;4DACP,CAAC,OAAO,EAAE,CAAC,EAAE;wDACf,CAAC;0DAGF,OAAO,EAAE;+CAVL,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAiB5B,6LAAC;wBACC,KAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAO,SAAS;gCAAwB,WAAU;;kDACjD,6LAAC,iJAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCACvB,eAAe,oBAAoB;;;;;;;0CAGtC,6LAAC;gCACC,WAAW,CAAC,gKAAgK,EAC1K,mBACI,yCACA,cACJ;gCACF,SAAS,mBAAmB,6BAA6B;;oCAC1D;oCAEE,mCACC,6LAAC,iJAAA,CAAA,cAAW;wCAAC,WAAU;;;;;6DAEvB,6LAAC,iJAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;4BAG5B,oCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsE;;;;;;sDAGrF,6LAAC;4CAAI,WAAU;sDAAsE;;;;;;sDAGrF,6LAAC;4CAAI,WAAU;sDAAsE;;;;;;;;;;;;;;;;;0CAM3F,6LAAC;gCACC,WAAW,CAAC,gKAAgK,EAC1K,mBACI,yCACA,cACJ;gCACF,SAAS,mBAAmB,kBAAkB;;kDAE9C,6LAAC,iJAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGrC,6LAAC;gCACC,WAAW,CAAC,gKAAgK,EAC1K,mBACI,yCACA,cACJ;gCACF,SAAS,mBAAmB,oBAAoB;;kDAEhD,6LAAC,iJAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;YAM7C,aAAa,mCACZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAwB;;;;;;;;;;qCAGzC,6LAAC,wIAAA,CAAA,eAAY;gBACX,SAAS;gBACT,MAAM;gBACN,cAAc;gBACd,iBAAiB;gBACjB,aAAa;gBACb,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;gBACtB,cAAc;;;;;;YAKjB,iBAAiB,oCAChB,6LAAC,+IAAA,CAAA,sBAAmB;gBAClB,WAAW;gBACX,WAAW;gBACX,SAAS;oBACP,iBAAiB;oBACjB,sBAAsB;oBACtB,8CAA8C;oBAC9C,gBAAgB,CAAC;oBACjB,oBAAoB;gBACtB;gBACA,WAAW;gBACX,YAAY;gBACZ,qBAAqB,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG;gBACxD,qBACE,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,IAC/B,OAAO,IAAI,CAAC,cACT,GAAG,CAAC,CAAC,KAAO,WAAW,CAAC,OAAO,IAAI,EACnC,MAAM,CAAC,WACV,EAAE;;;;;;YAKX,0CACC,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,SAAS,IAAM,yBAAyB;gBACxC,WAAW,yBAAyB,SAAS;gBAC7C,OAAO,yBAAyB,KAAK;gBACrC,aAAa,yBAAyB,WAAW;gBACjD,mBAAmB,yBAAyB,iBAAiB;gBAC7D,oBAAoB,yBAAyB,kBAAkB;;;;;;YAIlE,oCACC,6LAAC,gJAAA,CAAA,UAAoB;gBACnB,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,YAAY;;;;;;;;;;;;AAKtB;GA3fM;;QACiB,qIAAA,CAAA,YAAS;QAIb,4JAAA,CAAA,cAAW;QACR,yLAAA,CAAA,iBAAc;QAM9B,8KAAA,CAAA,WAAQ;QAUuD,8KAAA,CAAA,WAAQ;QA6FpD,iLAAA,CAAA,cAAW;QAkCH,iLAAA,CAAA,cAAW;;;KArJtC;uCA6fS", "debugId": null}}]}