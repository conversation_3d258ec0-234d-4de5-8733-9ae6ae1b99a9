import { prisma } from "../utils/prisma";
import { InputType, QuestionOption } from "@prisma/client";

class ReportRepository {
  async generateReport(
    projectId: number,
    filters: {
      startDate?: Date;
      endDate?: Date;
      type?: string;
    }
  ) {
    const { startDate, endDate, type } = filters;

    // Get project with questions
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        questions: {
          include: {
            questionOptions: true,
          },
        },
      },
    });

    if (!project) {
      throw new Error("Project not found");
    }

    // Get form submissions with date filtering
    const submissions = await prisma.formSubmission.findMany({
      where: {
        projectId,
        ...(startDate && endDate
          ? {
              createdAt: {
                gte: startDate,
                lte: endDate,
              },
            }
          : {}),
      },
      include: {
        answers: {
          include: {
            questionOption: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Generate report data for each question - using Promise.all to handle async methods
    const reportDataPromises = project.questions.map(async (question) => {
      const questionAnswers = submissions.flatMap((submission) =>
        submission.answers.filter((answer) => answer.questionId === question.id)
      );

      const total = submissions.length;
      const answered = questionAnswers.length;

      // Generate different report types based on question type
      switch (question.inputType) {
        case "selectone":
        case "selectmany":
          return this.generateChoiceReport(
            question,
            questionAnswers,
            total,
            answered
          );
        case "number":
        case "decimal":
          return this.generateNumericReport(
            question,
            questionAnswers,
            total,
            answered
          );
        case "text":
          return this.generateTextReport(
            question,
            questionAnswers,
            total,
            answered
          );
        case "table":
          return this.generateTableReport(
            question,
            questionAnswers,
            total,
            answered
          );
        default:
          return this.generateDefaultReport(
            question,
            questionAnswers,
            total,
            answered
          );
      }
    });

    // Wait for all report data to be generated
    const reportData = await Promise.all(reportDataPromises);

    // Generate summary statistics
    const summary = {
      totalSubmissions: submissions.length,
      totalQuestions: project.questions.length,
      averageResponseRate:
        project.questions.length > 0 && submissions.length > 0
          ? Math.round(
              (reportData.reduce((acc, curr) => acc + curr.answered, 0) /
                (project.questions.length * submissions.length)) *
                100
            )
          : 0,
      submissionDates: submissions.map((s) => s.createdAt),
    };

    return {
      summary,
      data: reportData,
      metadata: {
        projectName: project.name,
        generatedAt: new Date(),
        filters: {
          type,
          startDate,
          endDate,
        },
      },
    };
  }

  private generateChoiceReport(
    question: any,
    answers: any[],
    total: number,
    answered: number
  ) {
 

    const optionsMap = new Map<number, QuestionOption>(
      question.questionOptions.map((option: QuestionOption) => [
        option.id,
        option,
      ])
    );

    const optionCounts = new Map<number, number>();

    answers.forEach((answer) => {
      const optionIds = answer.questionOptionId
        ? [answer.questionOptionId]
        : JSON.parse(answer.value || "[]");
      optionIds.forEach((id: number) => {
        optionCounts.set(id, (optionCounts.get(id) || 0) + 1);
      });
    });

    const table = Array.from(optionCounts.entries()).map(([id, count]) => {
      const option = optionsMap.get(id);
      return {
        value: option?.label || `Option ${id}`,
        frequency: count,
        percentage: Math.round((count / total) * 100 * 100) / 100,
      };
    });

    return {
      question: question.label,
      type: question.inputType,
      answered,
      total,
      table,
      chartData: {
        labels: table.map((row) => row.value),
        values: table.map((row) => row.frequency),
      },
    };
  }

  private generateNumericReport(
    question: any,
    answers: any[],
    total: number,
    answered: number
  ) {
    const numbers = answers
      .map((answer) => Number(answer.value))
      .filter((num) => !isNaN(num));

    const stats =
      numbers.length > 0
        ? {
            min: Math.min(...numbers),
            max: Math.max(...numbers),
            avg: numbers.reduce((a, b) => a + b, 0) / numbers.length,
          }
        : {
            min: 0,
            max: 0,
            avg: 0,
          };

    const table = numbers.map((value) => ({
      value: value.toString(),
      frequency: 1,
      percentage: Math.round((1 / total) * 100 * 100) / 100,
    }));

    return {
      question: question.label,
      type: question.inputType,
      answered,
      total,
      table,
      stats,
      chartData: {
        labels: ["Min", "Max", "Average"],
        values: [stats.min, stats.max, stats.avg],
      },
    };
  }

  private generateTextReport(
    question: any,
    answers: any[],
    total: number,
    answered: number
  ) {
    const valueCounts = new Map<string, number>();
    answers.forEach((answer) => {
      const value = answer.value || "";
      valueCounts.set(value, (valueCounts.get(value) || 0) + 1);
    });

    const table = Array.from(valueCounts.entries())
      .map(([value, frequency]) => ({
        value,
        frequency,
        percentage: Math.round((frequency / total) * 100 * 100) / 100,
      }))
      .sort((a, b) => b.frequency - a.frequency);

    return {
      question: question.label,
      type: question.inputType,
      answered,
      total,
      table,
      chartData: {
        labels: table.slice(0, 5).map((row) => row.value || "No response"),
        values: table.slice(0, 5).map((row) => row.frequency),
      },
    };
  }

  private generateDefaultReport(
    question: any,
    answers: any[],
    total: number,
    answered: number
  ) {
    const table = answers.map((answer) => ({
      value: answer.value || "No response",
      frequency: 1,
      percentage: Math.round((1 / total) * 100 * 100) / 100,
    }));

    return {
      question: question.label,
      type: question.inputType,
      answered,
      total,
      table,
      chartData: {
        labels: table.slice(0, 5).map((row) => row.value),
        values: table.slice(0, 5).map((row) => row.frequency),
      },
    };
  }

  /**
   * Helper function to standardize table answer format
   * This ensures consistent format for table answers
   */
  private standardizeTableAnswer(answer: any): Record<string, string> {
    const result: Record<string, string> = {};

    try {
      if (!answer.value) return result;

      let parsedValue;
      try {
        parsedValue = JSON.parse(answer.value);
      } catch (e) {
        return result;
      }

      // Handle different formats and convert to standard format

      // Format 1: Direct key-value pairs for cells
      if (typeof parsedValue === "object" && !Array.isArray(parsedValue)) {
        Object.entries(parsedValue).forEach(([key, value]) => {
          // Standardize key format to rowId_columnId
          let standardKey = key;
          if (key.includes("-")) {
            standardKey = key.replace("-", "_");
          } else if (key.includes("row_") && key.includes("col_")) {
            const match = key.match(/row_(\d+)_col_(\d+)/);
            if (match) {
              standardKey = `${match[1]}_${match[2]}`;
            }
          }

          if (value) {
            result[standardKey] = String(value);
          }
        });
      }

      // Format 2: Object with rowsId, columnId, value properties
      else if (parsedValue.rowsId && parsedValue.columnId) {
        const key = `${parsedValue.rowsId}_${parsedValue.columnId}`;
        if (parsedValue.value) {
          result[key] = String(parsedValue.value);
        }
      }

      // Format 3: Array of objects with rowsId, columnId, value properties
      else if (Array.isArray(parsedValue)) {
        parsedValue.forEach((item) => {
          if (item && item.rowsId && item.columnId) {
            const key = `${item.rowsId}_${item.columnId}`;
            if (item.value) {
              result[key] = String(item.value);
            }
          }
        });
      }
    } catch (e) {
      console.error("Error standardizing table answer:", e);
    }

    return result;
  }

  private async generateTableReport(
    question: any,
    answers: any[],
    total: number,
    answered: number
  ) {
    try {
      // Get table structure (columns and rows)
      const tableData = await prisma.question.findUnique({
        where: { id: question.id },
        include: {
          tableColumns: {
            include: {
              childColumns: true,
            },
            orderBy: {
              id: "asc",
            },
          },
          tableRows: {
            orderBy: {
              id: "asc",
            },
          },
        },
      });

      if (!tableData || !tableData.tableColumns || !tableData.tableRows) {
        // Fallback to default report if table structure is not found
        return this.generateDefaultReport(question, answers, total, answered);
      }

      // Process the answers to extract the table data
      const tableStructure = {
        columns: tableData.tableColumns.map((col) => ({
          id: col.id,
          name: col.columnName,
          parentId: col.parentColumnId,
          children: tableData.tableColumns
            .filter((c) => c.parentColumnId === col.id)
            .map((child) => ({
              id: child.id,
              name: child.columnName,
            })),
        })),
        rows: tableData.tableRows.map((row) => ({
          id: row.id,
          name: row.rowsName,
        })),
      };

      // Format the data for the frontend
      // First, get all cell values from the database
      const cellValues = await prisma.columnRow.findMany({
        where: {
          column: {
            questionId: question.id,
          },
        },
        include: {
          column: true,
          row: true,
        },
      });

      // Create a map of cell values for easier lookup
      const cellValueMap = new Map();
      cellValues.forEach((cell) => {
        const key = `${cell.rowsId}_${cell.columnId}`;
        cellValueMap.set(key, cell.value || "");
      });

      // The table data is already retrieved above in cellValues using ColumnRow model
      // No need for additional TableData query since it doesn't exist in the schema

      // Process answers to extract additional data
      const answerData = answers.map((answer) => {
        try {
          // Parse the answer value which should contain column and row IDs
          const parsedValue = JSON.parse(answer.value || "{}");

          // If the answer has rowId and columnId, add it to the cellValueMap
          if (parsedValue.rowsId && parsedValue.columnId && parsedValue.value) {
            const key = `${parsedValue.rowsId}_${parsedValue.columnId}`;
            cellValueMap.set(key, parsedValue.value);
          }

          return {
            value: parsedValue,
            rowId: parsedValue.rowsId,
            columnId: parsedValue.columnId,
            cellValue: parsedValue.value,
          };
        } catch (e) {
          return {
            value: answer.value || "No response",
            rowId: null,
            columnId: null,
            cellValue: null,
          };
        }
      });

      // Directly fetch all answers for this question
      const allAnswers = await prisma.answer.findMany({
        where: {
          questionId: question.id,
        },
        include: {
          formSubmission: true,
        },
      });

      // Process all answers to extract cell values using the standardization helper
      allAnswers.forEach((answer) => {
        try {
          if (answer.value) {
            // Use the standardization helper to get a consistent format
            const standardizedValues = this.standardizeTableAnswer(answer);

            // Add the standardized values to the cell value map
            Object.entries(standardizedValues).forEach(([key, value]) => {
              cellValueMap.set(key, value);
            });
          }
        } catch (e) {
          console.error("Error processing answer:", e);
        }
      });

      // Try to find any table-specific data in the answers
      // This is a last resort to find any table data
      try {
        const rawAnswers = await prisma.answer.findMany({
          where: {
            questionId: question.id,
          },
          select: {
            id: true,
            value: true,
            questionId: true,
            formSubmissionId: true,
          },
        });

        // Try to parse any JSON values in the raw answers
        rawAnswers.forEach((answer) => {
          try {
            if (answer.value) {
              let parsedValue: any;
              try {
                parsedValue = JSON.parse(answer.value);
              } catch (e) {
                return;
              }

              // If it's an object with table-like structure, extract the values
              if (
                typeof parsedValue === "object" &&
                !Array.isArray(parsedValue)
              ) {
                Object.entries(parsedValue).forEach(([key, value]) => {
                  if (key.includes("_") && value) {
                    cellValueMap.set(key, String(value));
                  }
                });
              }
            }
          } catch (e) {
            console.error("Error processing raw answer:", e);
          }
        });
      } catch (e) {
        console.error("Error querying raw answers:", e);
      }

      // Combine the cell values from the database with the answer data
      // Convert the Map to a plain object for serialization
      const cellValuesObject: Record<string, string> = {};
      cellValueMap.forEach((value, key) => {
        cellValuesObject[key] = value;
      });

      // Now let's focus on retrieving the actual submitted answers for this table
      // First, check if we have any answers in the formSubmission.answers collection
      const submittedAnswers = await prisma.formSubmission.findMany({
        where: {
          projectId: question.projectId,
        },
        include: {
          answers: {
            where: {
              questionId: question.id,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      // Process all submitted answers to extract table cell values
      // Use a Map to collect all submissions for each cell
      const cellSubmissions = new Map<string, string[]>();
      let foundRealData = false;

      submittedAnswers.forEach((submission) => {
        submission.answers.forEach((answer) => {
          try {
            if (answer.value) {
              // Try to parse the answer value
              let parsedValue;
              try {
                parsedValue = JSON.parse(answer.value);
              } catch (e) {
                // Not a JSON value, skip
                return;
              }

              // Check if this is a table answer
              if (typeof parsedValue === "object") {
                // Handle different possible formats

                // Format 1: Direct key-value pairs for cells
                if (!Array.isArray(parsedValue)) {
                  Object.entries(parsedValue).forEach(([key, value]) => {
                    // Standardize key format to rowId_columnId
                    let standardKey = key;
                    if (key.includes("-")) {
                      standardKey = key.replace("-", "_");
                    } else if (key.includes("row_") && key.includes("col_")) {
                      const match = key.match(/row_(\d+)_col_(\d+)/);
                      if (match) {
                        standardKey = `${match[1]}_${match[2]}`;
                      }
                    }

                    if (value) {
                      // Collect all submissions for this cell
                      if (!cellSubmissions.has(standardKey)) {
                        cellSubmissions.set(standardKey, []);
                      }
                      cellSubmissions.get(standardKey)!.push(String(value));
                      foundRealData = true;
                    }
                  });
                }

                // Format 2: Object with rowsId, columnId, value properties
                else if (
                  typeof parsedValue === "object" &&
                  parsedValue !== null &&
                  "rowsId" in parsedValue &&
                  "columnId" in parsedValue
                ) {
                  const key = `${parsedValue.rowsId}_${parsedValue.columnId}`;
                  if ("value" in parsedValue && parsedValue.value) {
                    // Collect all submissions for this cell
                    if (!cellSubmissions.has(key)) {
                      cellSubmissions.set(key, []);
                    }
                    cellSubmissions.get(key)!.push(String(parsedValue.value));
                    foundRealData = true;
                  }
                }

                // Format 3: Array of objects with rowsId, columnId, value properties
                else if (Array.isArray(parsedValue)) {
                  parsedValue.forEach((item: any) => {
                    if (
                      item &&
                      typeof item === "object" &&
                      "rowsId" in item &&
                      "columnId" in item
                    ) {
                      const key = `${item.rowsId}_${item.columnId}`;
                      if ("value" in item && item.value) {
                        // Collect all submissions for this cell
                        if (!cellSubmissions.has(key)) {
                          cellSubmissions.set(key, []);
                        }
                        cellSubmissions.get(key)!.push(String(item.value));
                        foundRealData = true;
                      }
                    }
                  });
                }
              }
            }
          } catch (e) {
            console.error("Error processing submitted answer:", e);
          }
        });
      });

      // Convert collected submissions to aggregated cell values
      // For reports, we'll show all unique values separated by commas
      cellSubmissions.forEach((values, key) => {
        // Get unique values and join them
        const uniqueValues = [...new Set(values)];
        if (uniqueValues.length === 1) {
          // If all submissions have the same value, show it once
          cellValuesObject[key] = uniqueValues[0];
        } else {
          // If there are different values, show all of them with count
          const valueCounts = new Map<string, number>();
          values.forEach((value) => {
            valueCounts.set(value, (valueCounts.get(value) || 0) + 1);
          });

          const aggregatedValue = Array.from(valueCounts.entries())
            .map(([value, count]) =>
              count > 1 ? `${value} (${count}x)` : value
            )
            .join(", ");

          cellValuesObject[key] = aggregatedValue;
        }
      });

      // If we still have no real data, add some placeholder data
      if (!foundRealData && Object.keys(cellValuesObject).length === 0) {
        tableStructure.rows.forEach((row) => {
          tableStructure.columns.forEach((col) => {
            if (col.parentId === null) {
              const key = `${row.id}_${col.id}`;
              cellValuesObject[key] = "No data";
            }
          });
        });
      }

      // Create a standardized format for the table data
      // This will make it easier to work with in the frontend

      // Create a mapping of column IDs to column names for the frontend
      const columnNameMap: Record<string, string> = {};
      tableStructure.columns.forEach((col) => {
        columnNameMap[col.id] = col.name;
      });

      const formattedTable = {
        structure: tableStructure,
        data: answerData,
        cellValues: cellValuesObject,
        // Add column name mapping to ensure proper display in the frontend
        columnNameMap,
        // Add metadata to help with rendering
        metadata: {
          hasRealData: foundRealData,
          rowCount: tableStructure.rows.length,
          columnCount: tableStructure.columns.length,
          totalSubmissions: submittedAnswers.length,
          submissionsWithData: submittedAnswers.filter(
            (s) => s.answers.length > 0
          ).length,
          lastUpdated: new Date().toISOString(),
        },
      };

      // Generate meaningful chart data for the table
      // Count responses per row for a simple visualization
      const rowCounts = new Map();
      tableStructure.rows.forEach((row) => {
        let count = 0;
        tableStructure.columns.forEach((col) => {
          const key = `${row.id}_${col.id}`;
          if (cellValueMap.has(key) && cellValueMap.get(key)) {
            count++;
          }
          // Also check child columns
          col.children.forEach((child) => {
            const childKey = `${row.id}_${child.id}`;
            if (cellValueMap.has(childKey) && cellValueMap.get(childKey)) {
              count++;
            }
          });
        });
        rowCounts.set(row.id, count);
      });

      return {
        question: question.label,
        type: question.inputType,
        answered,
        total,
        table: formattedTable,
        chartData: {
          labels: tableStructure.rows.map((row) => row.name),
          values: tableStructure.rows.map((row) => rowCounts.get(row.id) || 0),
        },
      };
    } catch (error) {
      console.error("Error generating table report:", error);
      // Fallback to default report if there's an error
      return this.generateDefaultReport(question, answers, total, answered);
    }
  }
}

export default new ReportRepository();
