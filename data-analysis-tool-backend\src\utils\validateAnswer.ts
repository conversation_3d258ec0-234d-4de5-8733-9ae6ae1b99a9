// Step 2: Create a function that returns the validation schema based on InputType
import { InputType } from "@prisma/client";
import { z } from "zod";

function getAnswerSchemaByInputType(inputType: InputType) {
  switch (inputType) {
    case "text":
      return z.string({ invalid_type_error: "must be a text" });

    case "number":
      return z.number({ invalid_type_error: "Must be a number" });

    case "decimal":
      return z.number({ invalid_type_error: "Must be a decimal number" });

    case "selectone":
      return z.string().min(1, { message: "Must select one option" });

    case "selectmany":
      return z
        .array(z.string())
        .min(1, { message: "Must select at least one option" });

    case "date":
      return z.string().refine((val) => !isNaN(Date.parse(val)), {
        message: "Invalid date format",
      });

    case "dateandtime":
      return z.string().refine((val) => !isNaN(Date.parse(val)), {
        message: "Invalid date and time format",
      });

    default:
      throw new Error(`Unsupported input type: ${inputType}`);
  }
}

export function validateInput(inputType: InputType, answer: any) {
  const schema = getAnswerSchemaByInputType(inputType);

  const result = schema.safeParse(answer);

  if (result.success) {
    return { valid: true };
  } else {
    console.error("❌ Validation error:", result.error.errors);
    return { valid: false, errors: result.error.errors };
  }
}
