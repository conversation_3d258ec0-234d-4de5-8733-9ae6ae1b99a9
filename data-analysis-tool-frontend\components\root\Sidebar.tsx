import React, { useState } from "react";
import { RiFileList3Fill } from "react-icons/ri";
import { LuLibrary } from "react-icons/lu";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { showCreateProjectModal } from "@/redux/slices/createProjectSlice";
import { showCreateLibraryModal } from "@/redux/slices/createLibrarySlice";
import useNavItems from "../data/SidebarItems";
import Spinner from "../general/Spinner";
import { encode } from "@/lib/encodeDecode";
import { Project } from "@/types";

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
  topOffset: number;
  style?: React.CSSProperties;
  onNewProject?: () => void;
}

interface SidebarItemProps {
  href: string;
  label: string;
  icon: React.ElementType;
  count: number;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  href,
  label,
  icon: Icon,
  count,
}) => {
  const pathname = usePathname();
  const router = useRouter();
  const { deployedProjects, draftStatusProjects, archivedProjects } =
    useNavItems();
  const [isOpen, setIsOpen] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    if (label === "Deployed" || label === "Draft" || label === "Archived") {
      if (count > 0) {
        e.preventDefault();
        setIsOpen(!isOpen);
      } else {
        e.preventDefault();
        router.push(`${href}/not-available`);
      }
    }
    // For library categories
    // else if (label === "My Library" || label === "Collections") {
    //   if (count === 0) {
    //     e.preventDefault();
    //     // Navigate to the "not available" page for library
    //     router.push(/library/not-available);
    //   }
    // }
  };

  const handleProjectClick = (projectId: number) => {
    const encryptedProjectId = encode(projectId);
    router.push(`/project/${encryptedProjectId}/overview`);
  };

  const getProjects = (): Project[] => {
    switch (label) {
      case "Draft":
        return draftStatusProjects;
      case "Deployed":
        return deployedProjects;
      case "Archived":
        return archivedProjects;
      default:
        return [];
    }
  };

  const getProjectPath = (projectId: number) => {
    const encryptedProjectId = encode(projectId);
    return `/project/${encryptedProjectId}/overview`;
  };

  const currentProjects = getProjects();

  return (
    <li>
      <Link
        href={href}
        onClick={handleClick}
        className="flex items-center px-4 py-3 hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md text-neutral-800"
      >
        <Icon className="mr-2" size={20} />
        <span className="text-sm font-bold">{label}</span>
        <span className="ml-auto bg-neutral-200 text-neutral-700 rounded-full px-2 py-0.5 text-xs">
          {count}
        </span>
      </Link>

      {isOpen && currentProjects.length > 0 && (
        <div className="ml-6 mt-1 space-y-1">
          {currentProjects.map((project) => (
            <div
              key={project.id}
              onClick={() => handleProjectClick(project.id)}
              className={`flex items-center px-4 py-2 text-sm hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300 ease-in-out rounded-md cursor-pointer ${
                pathname === getProjectPath(project.id)
                  ? "bg-primary-500 text-neutral-100"
                  : ""
              }`}
            >
              {project.name}
            </div>
          ))}
        </div>
      )}
    </li>
  );
};

const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  topOffset,
  onNewProject,
}) => {
  const pathname = usePathname();
  const dispatch = useDispatch();
  const { navItems, deployedProjects, draftStatusProjects, archivedProjects } =
    useNavItems();

  const activeCategory = pathname.includes("/library") ? "library" : "project";

  const handleNewButtonClick = () => {
    if (activeCategory === "project") {
      if (onNewProject) {
        onNewProject();
      } else {
        // If no callback provided, use Redux (fallback for compatibility)
        dispatch(showCreateProjectModal());
      }
    } else {
      dispatch(showCreateLibraryModal());
    }
  };

  const filteredNavItems = navItems.filter(
    (item) => item.category === activeCategory
  );

  return (
    <aside
      style={{ top: `${topOffset}px`, height: `calc(100vh - ${topOffset}px)` }}
      className={`
        ${isOpen ? "translate-x-0" : "-translate-x-full"}
        fixed left-0 h-full w-64 shadow-xl bg-neutral-100 z-30
        transform transition-all duration-300 ease-in-out
        laptop:translate-x-0 laptop:static laptop:flex flex
      `}
    >
      {/* Left Icon Column */}
      <div className="w-1/5 bg-neutral-200 p-4">
        <div className="flex flex-col gap-5 items-center">
          <Link
            href="/dashboard"
            aria-label="Projects"
            className={`p-2  hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer
              ${
                activeCategory === "project"
                  ? "bg-primary-500 text-neutral-100 rounded-full"
                  : "text-neutral-700 hover:bg-primary-500 hover:text-neutral-100"
              }
            `}
          >
            <RiFileList3Fill size={20} title="Projects" />
          </Link>
          <Link
            href="/library"
            aria-label="Library"
            className={`p-2 hover:bg-primary-500 hover:rounded-full transition-all duration-300 ease-in-out cursor-pointer
              ${
                activeCategory === "library"
                  ? "bg-primary-500 text-neutral-100 rounded-full"
                  : "text-neutral-700 hover:bg-primary-500 hover:text-neutral-100"
              }
            `}
          >
            <LuLibrary size={20} title="Library" />
          </Link>
        </div>
      </div>

      {/* Right Navigation Area */}
      <div className="w-4/5 bg-neutral-100 flex-1">
        <div className="p-4">
          <button onClick={handleNewButtonClick} className="btn-primary w-full">
            {activeCategory === "project" ? "NEW PROJECT" : "NEW ITEM"}
          </button>
        </div>

        <nav className="mt-2">
          {/* {isLoading ? (
            <div className="flex justify-center p-4">
              <Spinner />
            </div>
          ) : ( */}
          <ul className="space-y-1">
            {filteredNavItems.map((item) => (
              <SidebarItem
                key={item.id}
                href={item.href}
                label={item.label}
                icon={item.icon}
                count={item.count}
              />
            ))}
          </ul>

          {/* )} */}
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;
