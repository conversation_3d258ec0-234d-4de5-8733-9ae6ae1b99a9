globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/edit-submission/[hashedId]/[submissionId]/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/app_favicon_ico_mjs_659ce808._.js"],"async":false},"[project]/providers/ReduxProvider.tsx <module evaluation>":{"id":"[project]/providers/ReduxProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_cd1072dd._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_c02dd60e._.js","/_next/static/chunks/node_modules_4fdaa9a7._.js","/_next/static/chunks/_6a075fa9._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/providers/ReduxProvider.tsx":{"id":"[project]/providers/ReduxProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_cd1072dd._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_c02dd60e._.js","/_next/static/chunks/node_modules_4fdaa9a7._.js","/_next/static/chunks/_6a075fa9._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/components/general/Notification.tsx <module evaluation>":{"id":"[project]/components/general/Notification.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_cd1072dd._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_c02dd60e._.js","/_next/static/chunks/node_modules_4fdaa9a7._.js","/_next/static/chunks/_6a075fa9._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/components/general/Notification.tsx":{"id":"[project]/components/general/Notification.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_cd1072dd._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_c02dd60e._.js","/_next/static/chunks/node_modules_4fdaa9a7._.js","/_next/static/chunks/_6a075fa9._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/providers/ReactQueryProvider.tsx <module evaluation>":{"id":"[project]/providers/ReactQueryProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_cd1072dd._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_c02dd60e._.js","/_next/static/chunks/node_modules_4fdaa9a7._.js","/_next/static/chunks/_6a075fa9._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/providers/ReactQueryProvider.tsx":{"id":"[project]/providers/ReactQueryProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_cd1072dd._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_c02dd60e._.js","/_next/static/chunks/node_modules_4fdaa9a7._.js","/_next/static/chunks/_6a075fa9._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js"],"async":false},"[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx <module evaluation>":{"id":"[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_cd1072dd._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_c02dd60e._.js","/_next/static/chunks/node_modules_4fdaa9a7._.js","/_next/static/chunks/_6a075fa9._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_d6c70e75._.js","/_next/static/chunks/node_modules_8315a1b3._.js","/_next/static/chunks/app_edit-submission_%5BhashedId%5D_%5BsubmissionId%5D_page_tsx_31c3df8f._.js"],"async":false},"[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx":{"id":"[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_cd1072dd._.js","/_next/static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","/_next/static/chunks/node_modules_%40tanstack_query-devtools_build_c02dd60e._.js","/_next/static/chunks/node_modules_4fdaa9a7._.js","/_next/static/chunks/_6a075fa9._.js","/_next/static/chunks/app_layout_tsx_c0237562._.js","/_next/static/chunks/_d6c70e75._.js","/_next/static/chunks/node_modules_8315a1b3._.js","/_next/static/chunks/app_edit-submission_%5BhashedId%5D_%5BsubmissionId%5D_page_tsx_31c3df8f._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/providers/ReduxProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/providers/ReduxProvider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_@tanstack_query-devtools_build_364110bc._.js","server/chunks/ssr/node_modules_framer-motion_dist_es_d9316aa7._.js","server/chunks/ssr/node_modules_@tanstack_query-devtools_build_873a4db7._.js","server/chunks/ssr/node_modules_be3e9093._.js","server/chunks/ssr/[root-of-the-server]__3beaeaf9._.js"],"async":false}},"[project]/components/general/Notification.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/general/Notification.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_@tanstack_query-devtools_build_364110bc._.js","server/chunks/ssr/node_modules_framer-motion_dist_es_d9316aa7._.js","server/chunks/ssr/node_modules_@tanstack_query-devtools_build_873a4db7._.js","server/chunks/ssr/node_modules_be3e9093._.js","server/chunks/ssr/[root-of-the-server]__3beaeaf9._.js"],"async":false}},"[project]/providers/ReactQueryProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/providers/ReactQueryProvider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_@tanstack_query-devtools_build_364110bc._.js","server/chunks/ssr/node_modules_framer-motion_dist_es_d9316aa7._.js","server/chunks/ssr/node_modules_@tanstack_query-devtools_build_873a4db7._.js","server/chunks/ssr/node_modules_be3e9093._.js","server/chunks/ssr/[root-of-the-server]__3beaeaf9._.js"],"async":false}},"[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_@tanstack_query-devtools_build_364110bc._.js","server/chunks/ssr/node_modules_framer-motion_dist_es_d9316aa7._.js","server/chunks/ssr/node_modules_@tanstack_query-devtools_build_873a4db7._.js","server/chunks/ssr/node_modules_be3e9093._.js","server/chunks/ssr/[root-of-the-server]__3beaeaf9._.js","server/chunks/ssr/[root-of-the-server]__53061a0f._.js","server/chunks/ssr/node_modules_9cd3da73._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/providers/ReduxProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/providers/ReduxProvider.tsx (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/components/general/Notification.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/components/general/Notification.tsx (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/providers/ReactQueryProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/providers/ReactQueryProvider.tsx (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}},"[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/edit-submission/[hashedId]/[submissionId]/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/edit-submission/[hashedId]/[submissionId]/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/app/favicon.ico":[],"[project]/app/layout":[{"path":"static/chunks/[root-of-the-server]__1c3928b8._.css","inlined":false}],"[project]/app/edit-submission/[hashedId]/[submissionId]/page":[{"path":"static/chunks/[root-of-the-server]__1c3928b8._.css","inlined":false}]},"entryJSFiles":{"[project]/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/app_favicon_ico_mjs_659ce808._.js"],"[project]/app/layout":["static/chunks/node_modules_@tanstack_query-devtools_build_cd1072dd._.js","static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","static/chunks/node_modules_@tanstack_query-devtools_build_c02dd60e._.js","static/chunks/node_modules_4fdaa9a7._.js","static/chunks/_6a075fa9._.js","static/chunks/app_layout_tsx_c0237562._.js"],"[project]/app/edit-submission/[hashedId]/[submissionId]/page":["static/chunks/node_modules_@tanstack_query-devtools_build_cd1072dd._.js","static/chunks/node_modules_framer-motion_dist_es_d12c975d._.js","static/chunks/node_modules_@tanstack_query-devtools_build_c02dd60e._.js","static/chunks/node_modules_4fdaa9a7._.js","static/chunks/_6a075fa9._.js","static/chunks/app_layout_tsx_c0237562._.js","static/chunks/_d6c70e75._.js","static/chunks/node_modules_8315a1b3._.js","static/chunks/app_edit-submission_[hashedId]_[submissionId]_page_tsx_31c3df8f._.js"]}}
