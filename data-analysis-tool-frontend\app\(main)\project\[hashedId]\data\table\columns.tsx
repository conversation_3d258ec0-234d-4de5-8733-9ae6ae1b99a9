"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowUpDown, Eye } from "lucide-react";
import { formatDate } from "@/lib/utils";
import React, { useState, useEffect } from "react";
import TableDataViewModal from "@/components/modals/TableDataViewModal";
import axios from "@/lib/axios";
import { BiSolidEdit } from "react-icons/bi";

// Type for a single submission
export type Submission = {
  id?: number;

  answers: {
    id: number;
    value: string | number;
    question: {
      inputType: string;
      id: number;
      label: string;
      type?: string;
    };
  }[];
  submissionTime?: string;
  submittedBy?: string;
  user?: {
    id: number;
    name: string;
    email: string;
  };
};

// Function to format cell value based on data type
const formatCellValue = (value: any, type?: string): string => {
  if (value === null || value === undefined) return "-";

  if (typeof value === "boolean") {
    return value ? "Yes" : "No";
  }

  if (value instanceof Date) {
    return formatDate(value);
  }

  if (type === "date" && typeof value === "string") {
    try {
      return formatDate(new Date(value));
    } catch {
      return value;
    }
  }

  return String(value);
};

// Function to fetch table structure (columns and rows) from the backend
const fetchTableStructure = async (questionId: number) => {
  try {
    let tableData = null;

    // Use the table-questions endpoint (the working one)
    try {
      const { data } = await axios.get(`/table-questions/${questionId}`);
      if (data && data.success && data.data) {
        // The response structure is { success: true, data: { question: {...}, cellValues: {...} } }
        tableData = data.data.question;
      } else {
      }
    } catch (err) {
      console.error("Error fetching from /table-questions/ endpoint:", err);
    }

    // If we still don't have tableRows, try to fetch them separately
    if (tableData && tableData.tableColumns && !tableData.tableRows) {
      try {
        const { data } = await axios.get(`/table-rows/${questionId}`);
        if (data && data.data && data.data.tableRows) {
          tableData.tableRows = data.data.tableRows;
        }
      } catch (err) {
        console.error("Error fetching table rows separately:", err);
      }
    }

    if (!tableData) {
      // Return a default structure instead of null to prevent errors
      return {
        id: questionId,
        label: "Table Data",
        tableColumns: [],
        tableRows: [
          { id: 1, rowsName: "Row 1" },
          { id: 2, rowsName: "Row 2" },
          { id: 3, rowsName: "Row 3" },
        ],
      };
    }

    // Ensure tableColumns exists and flatten nested structure
    if (!tableData.tableColumns || !Array.isArray(tableData.tableColumns)) {
      console.error(
        "tableColumns is missing or not an array, creating default tableColumns"
      );
      tableData.tableColumns = [];
    } else {
      // Flatten the nested structure to include child columns
      const flattenedColumns: any[] = [];

      tableData.tableColumns.forEach((column: any) => {
        // Add the parent column
        flattenedColumns.push({
          id: column.id,
          columnName: column.columnName,
          parentColumnId: column.parentColumnId || null,
        });

        // Add child columns if they exist
        if (column.childColumns && Array.isArray(column.childColumns)) {
          column.childColumns.forEach((childColumn: any) => {
            flattenedColumns.push({
              id: childColumn.id,
              columnName: childColumn.columnName,
              parentColumnId: childColumn.parentColumnId || column.id,
            });
          });
        }
      });

      tableData.tableColumns = flattenedColumns;
    }

    // Ensure tableRows exists
    if (!tableData.tableRows || !Array.isArray(tableData.tableRows)) {
      console.error(
        "tableRows is missing or not an array, creating default tableRows"
      );

      // Create dummy tableRows if none exist and tableColumns exists
      if (tableData.tableColumns && tableData.tableColumns.length > 0) {
        tableData.tableRows = tableData.tableColumns.map(
          (col: { id: number; columnName: string }) => ({
            id: col.id,
            rowsName: `Row ${col.id}`,
          })
        );
      } else {
        // If tableColumns doesn't exist or is empty, create a default tableRows array
        tableData.tableRows = [
          { id: 1, rowsName: "Row 1" },
          { id: 2, rowsName: "Row 2" },
          { id: 3, rowsName: "Row 3" },
        ];
      }
    }

    return tableData;
  } catch (error) {
    console.error("Error fetching table structure:", error);
    // Return a default structure instead of null to prevent errors
    const defaultStructure = {
      id: questionId,
      label: "Table Data",
      tableColumns: [],
      tableRows: [
        { id: 1, rowsName: "Row 1" },
        { id: 2, rowsName: "Row 2" },
        { id: 3, rowsName: "Row 3" },
      ],
    };
    return defaultStructure;
  }
};

// Helper function to generate columns from a sample submission
export const generateColumns = (
  onViewSubmission: (submission: Submission) => void,
  Submission?: Submission,
  hashedId?: string,
  allQuestions?: any[] // All questions from the project including conditional ones
): ColumnDef<Submission>[] => {
  // Base column for ID or index
  const baseColumns: ColumnDef<Submission>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          className="w-5 h-5 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 border-neutral-100 cursor-pointer"
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className="w-5 h-5 bg-neutral-100 border-neutral-400 data-[state=checked]:bg-white data-[state=checked]:text-primary-500 cursor-pointer"
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableHiding: false,
    },
    {
      id: "id",
      header: ({ column }) => {
        return (
          <div className="flex items-center hover:text-neutral-300">
            <span>ID</span>
            <span className="ml-2 flex-shrink-0 w-4 h-4">
              <ArrowUpDown
                className="w-full h-full"
                onClick={() =>
                  column.toggleSorting(column.getIsSorted() === "asc")
                }
              />
            </span>
          </div>
        );
      },
      accessorFn: (_, rowIndex) => rowIndex + 1, // Use row index as ID
      enableSorting: true,
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-8 font-medium text-neutral-700">
            {row.index + 1}
            <span className="flex items-center gap-2">
              <Eye
                onClick={() => onViewSubmission(row.original)}
                className="w-4 h-4 cursor-pointer hover:text-primary-500"
              />

              <BiSolidEdit
                className="w-4 h-4 cursor-pointer hover:text-primary-500"
                title="Edit"
                onClick={() => {
                  const submissionId = row.original.id;
                  if (!submissionId || !hashedId) return;
                  window.open(
                    `/edit-submission/${hashedId}/${submissionId}`,
                    "_blank"
                  );
                }}
              />
            </span>
          </div>
        );
      },
    },
    {
      id: "validation",
      header: "Validation",
      accessorKey: "validation",
    },
  ];

  // Use allQuestions if provided, otherwise fall back to questions from submissions
  let questionsToUse: any[] = [];

  if (allQuestions && allQuestions.length > 0) {
    // Use ALL questions from the project (including conditional ones)
    questionsToUse = allQuestions;
  } else if (Submission && Submission.answers?.length) {
    // Fallback: use unique questions from submissions (old behavior)
    questionsToUse = Array.from(
      new Map(
        Submission.answers.map((a) => [a.question.id, a.question])
      ).values()
    );
  } else {
    // No questions available
    return baseColumns;
  }

  // Generate dynamic question columns
  const questionColumns: ColumnDef<Submission>[] = questionsToUse.map(
    (question) => ({
      id: `${question.label}`,
      header: ({ column }) => (
        <div className="flex items-center justify-between">
          <span>{question.label}</span>
          <ArrowUpDown
            className="ml-1 h-4 w-4 cursor-pointer opacity-60"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          />
        </div>
      ),
      accessorFn: (row) => {
        // Filter answers that match the question.id
        const matches = row.answers.filter(
          (a) => a.question?.id === question.id
        );

        // If no matches found, this question wasn't answered (likely due to conditional logic)
        if (matches.length === 0) {
          console.error(
            `No answer found for question "${question.label}" (ID: ${question.id}) in submission ${row.id} - likely conditional question not triggered`
          );
          return null; // Will be displayed as "-" or "N/A"
        }

        // Handle selectmany vs normal input
        if (question.inputType === "selectmany") {
          // For selectmany, each selected option creates a separate answer record
          // We need to collect all the values and join them properly
          const values = matches
            .map((m) => m.value)
            .filter((v) => v && String(v).trim() !== "") // Filter out empty values
            .sort(); // Sort for consistent display
          return values.length > 0 ? values.join(", ") : null;
        }

        // For selectone and other input types, return the first (and should be only) value
        return matches[0]?.value ?? null;
      },
      cell: ({ getValue }) => {
        const value = getValue();

        // Handle table input type differently
        if (question.inputType === "table") {
          try {
            // First, ensure we're working with a string
            let valueStr = typeof value === "string" ? value : String(value);

            // Check if the value is already a JSON string
            let tableData;

            // Try to parse if it looks like JSON
            if (valueStr.startsWith("[") && valueStr.includes("{")) {
              try {
                tableData = JSON.parse(valueStr);
              } catch (parseError) {
                console.error("Failed to parse JSON string:", parseError);

                // If it's a malformed JSON string, try to clean it up
                // This handles cases where the JSON might have extra quotes or escaping
                valueStr = valueStr
                  .replace(/\\"/g, '"')
                  .replace(/^"/, "")
                  .replace(/"$/, "");

                try {
                  tableData = JSON.parse(valueStr);
                } catch (secondParseError) {
                  console.error(
                    "Failed second parse attempt:",
                    secondParseError
                  );

                  // If it still fails, try to extract the array from the string
                  const match = valueStr.match(/\[([\s\S]*)\]/);
                  if (match && match[0]) {
                    try {
                      tableData = JSON.parse(match[0]);
                    } catch (thirdParseError) {
                      console.error(
                        "Failed third parse attempt:",
                        thirdParseError
                      );

                      // Special handling for the format in the screenshot
                      // Format: [{'columnId':18,'rowsId':15,'value':'Ram Babu Thapa'},...
                      try {
                        // Convert single quotes to double quotes for valid JSON
                        const fixedStr = match[0].replace(/'/g, '"');
                        tableData = JSON.parse(fixedStr);
                      } catch (fourthParseError) {
                        console.error(
                          "Failed fourth parse attempt:",
                          fourthParseError
                        );
                      }
                    }
                  }
                }
              }
            }

            // If we still don't have valid data, try to parse it as a custom format
            if (
              !tableData &&
              valueStr.includes("columnId") &&
              valueStr.includes("rowsId")
            ) {
              try {
                // Handle the format: [{'columnId':18,'rowsId':15,'value':'Ram Babu Thapa'},...]
                // Convert to valid JSON by replacing single quotes with double quotes
                const fixedStr = valueStr.replace(/'/g, '"');
                tableData = JSON.parse(fixedStr);
              } catch (customFormatError) {
                console.error(
                  "Failed custom format parsing:",
                  customFormatError
                );
              }
            }

            // If we have valid array data, display it as a table
            if (Array.isArray(tableData)) {
              // Process the table data to extract column and row information
              const processedData = tableData.map((item) => {
                // Get column name - use the actual column name if available
                let columnName = "";
                if (item.columnName) {
                  // If columnName is directly available in the data, use it
                  columnName = item.columnName;
                } else if (item.column && item.column.columnName) {
                  // If the column object is available with columnName property
                  columnName = item.column.columnName;
                } else if (item.columnId) {
                  // If we only have columnId, check if we have a name for it
                  // First, try to get the name from the item itself if it has a name property
                  if (item.name) {
                    columnName = item.name;
                  } else if (item.label) {
                    columnName = item.label;
                  } else {
                    // For now, store the columnId but we'll replace it with the actual name later
                    // We'll use the columnId as a key to look up the actual name when we fetch the table structure
                    columnName = String(item.columnId);
                  }
                }

                // Get row name - use the actual row name if available
                let rowName = "";
                if (item.rowsName) {
                  // If rowsName is directly available in the data, use it
                  rowName = item.rowsName;
                } else if (item.row && item.row.rowsName) {
                  // If the row object is available with rowsName property
                  rowName = item.row.rowsName;
                } else if (item.rowsId) {
                  // If we only have rowsId, check if we have a name for it
                  // First, try to get the name from the item itself if it has a name property
                  if (item.name) {
                    rowName = item.name;
                  } else if (item.label) {
                    rowName = item.label;
                  } else {
                    // Store the rowsId as a key to look up the actual name later
                    // We'll replace it with the actual name when we fetch the table structure
                    rowName = String(item.rowsId);
                  }
                }

                // Extract the value
                const cellValue = item.value !== undefined ? item.value : "";

                return {
                  column: columnName,
                  row: rowName,
                  value: cellValue,
                };
              });

              // Create a structured table with proper headers in the format of the second image
              // Group data by rows and columns to create a matrix
              const rowsMap = new Map<string, Map<string, string>>();
              const columnsSet = new Set<string>();

              // First pass: collect all unique rows and columns
              processedData.forEach((item) => {
                columnsSet.add(String(item.column));
                if (!rowsMap.has(String(item.row))) {
                  rowsMap.set(String(item.row), new Map<string, string>());
                }
                rowsMap
                  .get(String(item.row))
                  ?.set(String(item.column), String(item.value));
              });

              // Convert to arrays for rendering
              const uniqueColumns = Array.from(columnsSet);
              const uniqueRows = Array.from(rowsMap.keys());

              // Check if we should use the parent-child column structure
              // This should be determined by the table structure from the backend
              // Set to true to use the parent-child column structure
              const useParentChildColumns = true;

              // Create a TableCellWithModal component to handle state
              const TableCellWithModal = () => {
                const [showModal, setShowModal] = useState(false);
                const [tableStructure, setTableStructure] = useState<any>(null);
                const [loading, setLoading] = useState(false);

                // Debug effect to track tableStructure changes
                useEffect(() => {}, [tableStructure]);

                // Function to fetch table structure when the modal is opened
                const fetchTableData = async () => {
                  if (!question.id) {
                    console.error("No question ID available");
                    return;
                  }

                  setLoading(true);
                  try {
                    const structure = await fetchTableStructure(question.id);

                    if (structure) {
                      // Make sure tableRows is included in the structure
                      if (!structure.tableRows) {
                        console.error(
                          "tableRows is missing from the structure!"
                        );
                      }
                      setTableStructure(structure);

                      // Update the processed data with actual column and row names
                      if (structure.tableColumns && structure.tableRows) {
                        // Create maps for quick lookup
                        const columnMap = new Map();
                        const rowMap = new Map();

                        structure.tableColumns.forEach((col: any) => {
                          columnMap.set(col.id, col.columnName);
                          // Also map the string version of the ID
                          columnMap.set(String(col.id), col.columnName);
                        });

                        structure.tableRows.forEach((row: any) => {
                          rowMap.set(row.id, row.rowsName);
                          // Also map the string version of the ID
                          rowMap.set(String(row.id), row.rowsName);
                        });

                        // Update the processed data
                        processedData.forEach((item) => {
                          if (item.column) {
                            // Check if the column is a number (ID) or already a name
                            if (!isNaN(Number(item.column))) {
                              const columnId = item.column;

                              if (columnMap.has(columnId)) {
                                const oldColumn = item.column;
                                item.column = columnMap.get(columnId);
                              } else {
                                // If we don't have a mapping for this column ID, try to find a column with this ID
                                const matchingColumn =
                                  structure.tableColumns.find(
                                    (col: any) =>
                                      String(col.id) === String(columnId)
                                  );
                                if (matchingColumn) {
                                  const oldColumn = item.column;
                                  item.column = matchingColumn.columnName;
                                }
                              }
                            }
                          }

                          if (item.row) {
                            // Check if the row is a number (ID) or already a name
                            if (!isNaN(Number(item.row))) {
                              const rowId = item.row;

                              if (rowMap.has(rowId)) {
                                const oldRow = item.row;
                                item.row = rowMap.get(rowId);
                              } else {
                                // If we don't have a mapping for this row ID, try to find a row with this ID
                                const matchingRow = structure.tableRows.find(
                                  (row: any) => String(row.id) === String(rowId)
                                );
                                if (matchingRow) {
                                  const oldRow = item.row;
                                  item.row = matchingRow.rowsName;
                                }
                              }
                            }
                          }
                        });

                        // Rebuild the rowsMap with the updated column and row names
                        const newRowsMap = new Map<
                          string,
                          Map<string, string>
                        >();
                        const newColumnsSet = new Set<string>();

                        // First pass: collect all unique rows and columns with their updated names
                        processedData.forEach((item) => {
                          newColumnsSet.add(String(item.column));
                          if (!newRowsMap.has(String(item.row))) {
                            newRowsMap.set(
                              String(item.row),
                              new Map<string, string>()
                            );
                          }
                          newRowsMap
                            .get(String(item.row))
                            ?.set(String(item.column), String(item.value));
                        });

                        // Update the original arrays with the transformed data
                        uniqueColumns.length = 0;
                        uniqueRows.length = 0;

                        // Add the updated column and row names
                        newColumnsSet.forEach((col) => uniqueColumns.push(col));
                        newRowsMap.forEach((_, row) => uniqueRows.push(row));

                        // Clear the existing rowsMap and copy entries from newRowsMap
                        rowsMap.clear();
                        newRowsMap.forEach((valueMap, key) => {
                          rowsMap.set(key, valueMap);
                        });

                        // Log the final rowsMap for debugging
                      }
                    }
                  } catch (error) {
                    console.error("Error fetching table structure:", error);
                  } finally {
                    setLoading(false);
                  }
                };

                return (
                  <div className="font-medium text-neutral-700">
                    <a
                      href="#"
                      onClick={async (e) => {
                        e.preventDefault();
                        setLoading(true);
                        setShowModal(true);
                        await fetchTableData(); // Wait for table structure to load
                      }}
                      className="inline-flex items-center gap-1 text-primary-500 hover:text-primary-700 hover:underline whitespace-nowrap"
                    >
                      <Eye size={12} className="inline" /> Click to view table
                    </a>

                    {/* Debug tableStructure */}
                    {(() => {
                      return null;
                    })()}
                    <TableDataViewModal
                      isOpen={showModal}
                      onClose={() => setShowModal(false)}
                      title={question.label || "Table Data"}
                      tableData={processedData}
                      uniqueColumns={uniqueColumns}
                      uniqueRows={uniqueRows}
                      rowsMap={rowsMap}
                      useParentChildColumns={useParentChildColumns}
                      loading={loading}
                      tableStructure={tableStructure}
                    />
                  </div>
                );
              };

              return <TableCellWithModal />;
            }
          } catch (e) {
            // If parsing fails, log the error and the value that caused it
            console.error("Error parsing table data:", e, "Value:", value);
          }
        }

        // Default rendering for non-table data or if table parsing failed
        // Handle null/undefined values for conditional questions
        if (value === null || value === undefined || value === "") {
          return <div className="font-medium text-neutral-400 italic">-</div>;
        }

        return (
          <div className="font-medium text-neutral-700">{String(value)}</div>
        );
      },
      enableSorting: true,
    })
  );

  // Optional metadata columns
  const rearColumns: ColumnDef<Submission>[] = [
    {
      id: "submissionTime",
      header: ({ column }) => {
        return (
          <div className="flex items-center gap-4 hover:text-neutral-300">
            <span>Submission Time</span>
            <span className="ml-2 flex-shrink-0 w-4 h-4">
              <ArrowUpDown
                className="w-full h-full"
                onClick={() =>
                  column.toggleSorting(column.getIsSorted() === "asc")
                }
              />
            </span>
          </div>
        );
      },
      accessorKey: "submissionTime",
      cell: ({ getValue }) => {
        const value = getValue();
        return (
          <div className="font-medium text-neutral-700">
            {formatCellValue(value, "date") || "Not recorded"}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      id: "submittedBy",
      header: ({ column }) => {
        return (
          <div className="flex items-center gap-4 hover:text-neutral-300">
            <span>Submitted By</span>
            <span className="ml-2 flex-shrink-0 w-4 h-4">
              <ArrowUpDown
                className="w-full h-full"
                onClick={() =>
                  column.toggleSorting(column.getIsSorted() === "asc")
                }
              />
            </span>
          </div>
        );
      },
      accessorKey: "submittedBy",
      accessorFn: (row) => row.user?.name || "Anonymous",
      cell: ({ getValue }) => {
        const value = getValue() as string;
        return <div className="font-medium text-neutral-700">{value}</div>;
      },
      enableSorting: true,
    },
  ];

  return [...baseColumns, ...questionColumns, ...rearColumns];
};
