"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow as UITableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import {
  fetchTableStructure,
  TableColumn,
  TableRow as TableRowType,
  CellValue,
} from "../../lib/api/table";

interface TableInputProps {
  questionId: number;
  value: string | CellValue[];
  onChange: (value: CellValue[]) => void;
  required?: boolean;
  tableLabel?: string;
}

export function TableInput({
  questionId,
  value,
  onChange,
  required = false,
  tableLabel,
}: TableInputProps) {
  // All state hooks at the top of the component
  const [columns, setColumns] = useState<TableColumn[]>([]);
  const [rows, setRows] = useState<TableRowType[]>([]);
  const [cellValues, setCellValues] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tableInfo, setTableInfo] = useState<{ label?: string }>({});

  // Process columns to create a flat structure with parent-child relationships
  const processColumns = (tableData: any) => {
    if (!tableData || !tableData.tableColumns) return [];

    const flattenedColumns: TableColumn[] = [];
    const parentColumns = tableData.tableColumns.filter(
      (col: TableColumn) =>
        col.parentColumnId === null || col.parentColumnId === undefined
    );

    // Process each parent column and its children
    parentColumns.forEach((parentCol: TableColumn) => {
      // Add the parent column
      flattenedColumns.push(parentCol);

      // Add child columns if they exist
      if (parentCol.childColumns && parentCol.childColumns.length > 0) {
        parentCol.childColumns.forEach((childCol: any) => {
          flattenedColumns.push({
            id: childCol.id,
            columnName: childCol.columnName,
            parentColumnId: childCol.parentColumnId,
          });
        });
      }
    });

    return flattenedColumns;
  };

  // IMPORTANT: All hooks must be called unconditionally and in the same order every render
  // Group columns by parent-child relationships - always called, never conditional
  const groupedColumns = React.useMemo(() => {
    // Default empty values for when columns are not loaded yet
    if (columns.length === 0) {
      return {
        parentColumns: [],
        columnMap: new Map<number, TableColumn[]>(),
        hasChildColumns: false,
      };
    }

    // Get all parent columns (those without a parentColumnId)
    const parentColumns = columns.filter(
      (col) => col.parentColumnId === undefined || col.parentColumnId === null
    );

    // Create a map of parent columns to their child columns
    const columnMap = new Map<number, TableColumn[]>();

    parentColumns.forEach((parentCol) => {
      // Find all child columns for this parent
      const childColumns = columns.filter(
        (col) => col.parentColumnId === parentCol.id
      );
      columnMap.set(parentCol.id, childColumns);
    });

    // Check if any parent has child columns
    const hasChildColumns = parentColumns.some(
      (p) => (columnMap.get(p.id) || []).length > 0
    );

    return { parentColumns, columnMap, hasChildColumns };
  }, [columns]);

  // Fetch table structure (columns and rows) on component mount or when questionId changes
  useEffect(() => {
    const loadTableStructure = async () => {
      try {
        setLoading(true);

        const tableData = await fetchTableStructure(questionId);

        if (tableData) {
          // Check if tableColumns and tableRows exist
          if (!tableData.tableColumns || !tableData.tableRows) {
            console.error(
              "Missing tableColumns or tableRows in response:",
              tableData
            );
          }

          // Process columns to handle parent-child relationships
          const processedColumns = processColumns(tableData);
          setColumns(processedColumns);
          setRows(tableData.tableRows || []);

          // Store the table label if available
          if (tableData.label) {
            setTableInfo({ label: tableData.label });
          }

         
        } else {
          console.error("No table data returned");
          setError("Failed to load table structure");
        }
      } catch (err) {
        console.error("Error fetching table structure:", err);
        setError("Failed to load table structure");
      } finally {
        setLoading(false);
      }
    };

    loadTableStructure();
  }, [questionId]); // Only reload when questionId changes, not when value changes

  // Handle value changes separately without reloading the table structure
  useEffect(() => {
    // Don't process if we're still loading the table structure
    if (loading) return;

    // Initialize cell values from existing data if available
    const initialCellValues: Record<string, string> = {};

    // If value is a string, try to parse it as JSON
    let cellData: CellValue[] = [];
    if (typeof value === "string") {
      // Only attempt to parse if the string is not empty
      if (value && value.trim() !== "") {
        try {
          cellData = JSON.parse(value);
        } catch (e) {
          console.error("Error parsing cell data:", e);
          cellData = [];
        }
      } else {
        console.error("Empty string value, using empty array");
      }
    } else if (Array.isArray(value)) {
      cellData = value;
    }

    // Convert cell data to a map for easier access
    cellData.forEach((cell) => {
      initialCellValues[`${cell.columnId}_${cell.rowsId}`] = cell.value;
    });

    // Check if the value indicates a form reset (empty array, empty string, or undefined)
    const isFormReset =
      !value ||
      (typeof value === "string" && value.trim() === "") ||
      (Array.isArray(value) && value.length === 0);

    if (isFormReset) {
      // Clear all cell values when form is reset
      setCellValues({});
    } else if (Object.keys(initialCellValues).length > 0) {
      // Only update cell values if we have new data and we're not in the middle of editing
      setCellValues((prev) => {
        // Merge with existing values to avoid losing user input
        return { ...initialCellValues, ...prev };
      });
    }
  }, [value, loading]);

  // Handle cell value change
  const handleCellChange = (
    columnId: number,
    rowId: number,
    newValue: string
  ) => {
    const cellKey = `${columnId}_${rowId}`;

    // Update the cell values state
    setCellValues((prev) => ({
      ...prev,
      [cellKey]: newValue,
    }));

    // Use a setTimeout to ensure we're working with the latest state
    // This prevents the race condition where the state update hasn't completed yet
    setTimeout(() => {
      // Get the current state of cellValues after the update
      const currentCellValues = { ...cellValues, [cellKey]: newValue };

      // Convert the updated cell values to the format expected by the onChange handler
      const updatedCellValues: CellValue[] = [];

      // Convert all cell values to the expected format
      Object.entries(currentCellValues).forEach(([key, value]) => {
        if (value.trim() !== "") {
          const [colId, rowId] = key.split("_").map(Number);
          updatedCellValues.push({
            columnId: colId,
            rowsId: rowId,
            value,
          });
        }
      });

      // Call the onChange handler with all cell values
      onChange(updatedCellValues);
    }, 0);
  };

  // Calculate this once, outside of any conditional rendering
  // Only show error when there are no columns - having no rows is valid
  const hasNoColumns = columns.length === 0;

  // Render the hierarchical table
  // Use a single return statement with conditional rendering inside
  return (
    <div className="overflow-x-auto">
      {loading ? (
        <div className="py-4 text-center">Loading table...</div>
      ) : error ? (
        <div className="py-4 text-center text-red-500">{error}</div>
      ) : hasNoColumns ? (
        <div className="py-4 text-center text-amber-600">
          This table has no columns defined. Please configure the table question
          first.
        </div>
      ) : (
        <Table className="border-collapse">
          <TableHeader>
            {/* First row: Parent column headers starting from leftmost position */}
            <UITableRow>
              {groupedColumns.parentColumns.map((parentCol) => {
                const childColumns =
                  groupedColumns.columnMap.get(parentCol.id) || [];
                // If this parent has children, it spans multiple columns
                const colSpan = childColumns.length || 1;

                return (
                  <TableHead
                    key={parentCol.id}
                    colSpan={colSpan}
                    className="text-center border bg-blue-50 font-medium"
                  >
                    {parentCol.columnName}
                  </TableHead>
                );
              })}
            </UITableRow>

            {/* Second row: Child column headers (only if there are child columns) */}
            {groupedColumns.hasChildColumns && (
              <UITableRow>
                {groupedColumns.parentColumns.map((parentCol) => {
                  const childColumns =
                    groupedColumns.columnMap.get(parentCol.id) || [];

                  // If this parent has no children, render an empty cell to maintain alignment
                  if (childColumns.length === 0) {
                    return (
                      <TableHead
                        key={`empty-${parentCol.id}`}
                        className="border bg-blue-50/50 text-sm"
                      >
                        {/* Empty cell to maintain column alignment */}
                      </TableHead>
                    );
                  }

                  // Otherwise, render each child column
                  return childColumns.map((childCol) => (
                    <TableHead
                      key={childCol.id}
                      className="border bg-blue-50/50 text-sm"
                    >
                      {childCol.columnName}
                    </TableHead>
                  ));
                })}
              </UITableRow>
            )}
          </TableHeader>

          <TableBody>
            {rows.length > 0 ? (
              rows.map((row, rowIndex) => (
                <UITableRow
                  key={row.id}
                  className={rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}
                >
                  {/* Render cells for each parent column starting from leftmost position */}
                  {groupedColumns.parentColumns.map((parentCol) => {
                    const childColumns =
                      groupedColumns.columnMap.get(parentCol.id) || [];

                    // If this parent has no children, render a single cell
                    if (childColumns.length === 0) {
                      return (
                        <TableCell
                          key={`cell-${parentCol.id}-${row.id}`}
                          className="border p-1"
                        >
                          <Input
                            value={
                              cellValues[`${parentCol.id}_${row.id}`] || ""
                            }
                            onChange={(e) =>
                              handleCellChange(
                                parentCol.id,
                                row.id,
                                e.target.value
                              )
                            }
                            className="w-full"
                            required={required}
                            placeholder="Enter value"
                          />
                        </TableCell>
                      );
                    }

                    // Otherwise, render cells for each child column
                    return childColumns.map((childCol) => (
                      <TableCell
                        key={`cell-${childCol.id}-${row.id}`}
                        className="border p-1"
                      >
                        <Input
                          value={cellValues[`${childCol.id}_${row.id}`] || ""}
                          onChange={(e) =>
                            handleCellChange(
                              childCol.id,
                              row.id,
                              e.target.value
                            )
                          }
                          className="w-full"
                          required={required}
                          placeholder="Enter value"
                        />
                      </TableCell>
                    ));
                  })}
                </UITableRow>
              ))
            ) : (
              // When no rows exist, show a single row with input fields under columns
              <UITableRow>
                {/* Render input cells for each parent column starting from leftmost position */}
                {groupedColumns.parentColumns.map((parentCol) => {
                  const childColumns =
                    groupedColumns.columnMap.get(parentCol.id) || [];

                  // If this parent has no children, render a single cell
                  if (childColumns.length === 0) {
                    return (
                      <TableCell
                        key={`cell-${parentCol.id}-no-row`}
                        className="border p-1"
                      >
                        <Input
                          value={cellValues[`${parentCol.id}_no_row`] || ""}
                          onChange={(e) =>
                            handleCellChange(
                              parentCol.id,
                              "no_row" as any,
                              e.target.value
                            )
                          }
                          className="w-full"
                          required={required}
                          placeholder="Enter value"
                        />
                      </TableCell>
                    );
                  }

                  // Otherwise, render cells for each child column
                  return childColumns.map((childCol) => (
                    <TableCell
                      key={`cell-${childCol.id}-no-row`}
                      className="border p-1"
                    >
                      <Input
                        value={cellValues[`${childCol.id}_no_row`] || ""}
                        onChange={(e) =>
                          handleCellChange(
                            childCol.id,
                            "no_row" as any,
                            e.target.value
                          )
                        }
                        className="w-full"
                        required={required}
                        placeholder="Enter value"
                      />
                    </TableCell>
                  ));
                })}
              </UITableRow>
            )}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
