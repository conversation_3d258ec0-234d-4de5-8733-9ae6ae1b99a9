"use client";

import React from "react";
import Modal from "./Modal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface TableDataItem {
  column: string;
  row: string;
  value: string;
}

interface CellValue {
  columnId: number;
  rowsId: number;
  value: string;
}

interface TableColumn {
  id: number;
  columnName: string;
  parentColumnId?: number;
}

interface TableRowType {
  id: number;
  rowsName: string;
}

interface TableViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  tableData: TableDataItem[];
  uniqueColumns: string[];
  uniqueRows: string[];
  rowsMap: Map<string, Map<string, string>>;
  useParentChildColumns?: boolean;
  loading?: boolean;
  tableStructure?: {
    tableColumns?: TableColumn[];
    tableRows?: TableRowType[];
  };
}

const TableDataViewModal: React.FC<TableViewModalProps> = ({
  isOpen,
  onClose,
  title,
  tableData,
  uniqueColumns,
  uniqueRows,
  rowsMap,
  useParentChildColumns = false,
  loading = false,
  tableStructure,
}) => {
  // Parse the submitted table data to get the actual cell values
  const parsedTableData = React.useMemo(() => {
    if (!tableData || tableData.length === 0) return [];

    // Convert tableData to CellValue format
    const cellValues: CellValue[] = [];
    tableData.forEach((item) => {
      // Try to parse column and row as numbers (IDs)
      const columnId = parseInt(item.column);
      const rowId = parseInt(item.row);

      if (!isNaN(columnId) && !isNaN(rowId)) {
        cellValues.push({
          columnId,
          rowsId: rowId,
          value: item.value,
        });
      }
    });

    return cellValues;
  }, [tableData]);

  // Group columns by parent-child relationships (same logic as TableInput.tsx)
  const groupedColumns = React.useMemo(() => {
    if (
      !tableStructure?.tableColumns ||
      tableStructure.tableColumns.length === 0
    ) {
      return {
        parentColumns: [],
        columnMap: new Map<number, TableColumn[]>(),
        hasChildColumns: false,
      };
    }

    // Get all parent columns (those without a parentColumnId)
    const parentColumns = tableStructure.tableColumns.filter(
      (col) => col.parentColumnId === undefined || col.parentColumnId === null
    );

    // Create a map of parent columns to their child columns
    const columnMap = new Map<number, TableColumn[]>();

    parentColumns.forEach((parentCol) => {
      // Find all child columns for this parent
      const childColumns = tableStructure.tableColumns!.filter(
        (col) => col.parentColumnId === parentCol.id
      );
      columnMap.set(parentCol.id, childColumns);
    });

    // Check if any parent has child columns
    const hasChildColumns = parentColumns.some(
      (p) => (columnMap.get(p.id) || []).length > 0
    );

    return { parentColumns, columnMap, hasChildColumns };
  }, [tableStructure]);

  // Create a map for quick cell value lookup
  const cellValueMap = React.useMemo(() => {
    const map = new Map<string, string>();
    parsedTableData.forEach((cell) => {
      map.set(`${cell.columnId}_${cell.rowsId}`, cell.value);
    });
    return map;
  }, [parsedTableData]);
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="p-6 rounded-md max-w-4xl w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%]"
    >
      <div className="flex flex-col gap-4">
        <h2 className="text-xl font-semibold text-neutral-700">{title}</h2>

        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
            <span className="ml-2 text-neutral-600">Loading table data...</span>
          </div>
        ) : !tableStructure?.tableColumns ||
          tableStructure.tableColumns.length === 0 ? (
          <div className="py-4 text-center text-amber-600">
            <p>No table structure available.</p>
            <p className="text-sm mt-2">Debug info:</p>
            <pre className="text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(
                {
                  hasTableStructure: !!tableStructure,
                  tableColumnsLength: tableStructure?.tableColumns?.length || 0,
                  tableRowsLength: tableStructure?.tableRows?.length || 0,
                  tableDataLength: tableData?.length || 0,
                  useParentChildColumns,
                },
                null,
                2
              )}
            </pre>
          </div>
        ) : (
          <div className="overflow-auto max-h-[70vh]">
            <Table className="border-collapse border border-amber-700">
              <TableHeader className="bg-amber-100">
                {/* First row: Parent column headers */}
                <TableRow>
                  <TableHead
                    className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-100"
                    rowSpan={groupedColumns.hasChildColumns ? 2 : 1}
                  >
                    {title}
                  </TableHead>
                  {groupedColumns.parentColumns.map((parentCol) => {
                    const childColumns =
                      groupedColumns.columnMap.get(parentCol.id) || [];
                    // If this parent has children, it spans multiple columns
                    const colSpan = childColumns.length || 1;

                    return (
                      <TableHead
                        key={parentCol.id}
                        colSpan={colSpan}
                        className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider text-center border border-amber-700 bg-amber-100"
                      >
                        {parentCol.columnName}
                      </TableHead>
                    );
                  })}
                </TableRow>

                {/* Second row: Child column headers (only if there are child columns) */}
                {groupedColumns.hasChildColumns && (
                  <TableRow>
                    {groupedColumns.parentColumns.map((parentCol) => {
                      const childColumns =
                        groupedColumns.columnMap.get(parentCol.id) || [];

                      // If this parent has no children, render a placeholder
                      if (childColumns.length === 0) {
                        return null; // Don't render anything for parents without children
                      }

                      // Otherwise, render each child column
                      return childColumns.map((childCol) => (
                        <TableHead
                          key={childCol.id}
                          className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-50"
                        >
                          {childCol.columnName}
                        </TableHead>
                      ));
                    })}
                  </TableRow>
                )}
              </TableHeader>

              <TableBody>
                {tableStructure.tableRows?.map((row, rowIndex) => (
                  <TableRow key={row.id} className="bg-white">
                    <TableCell className="px-3 py-2 text-xs font-medium border border-amber-700 bg-amber-50">
                      {row.rowsName}
                    </TableCell>

                    {/* Render cells for each parent column */}
                    {groupedColumns.parentColumns.map((parentCol) => {
                      const childColumns =
                        groupedColumns.columnMap.get(parentCol.id) || [];

                      // If this parent has no children, render a single cell
                      if (childColumns.length === 0) {
                        return (
                          <TableCell
                            key={`cell-${parentCol.id}-${row.id}`}
                            className="px-3 py-2 text-xs border border-amber-700"
                          >
                            {cellValueMap.get(`${parentCol.id}_${row.id}`) ||
                              ""}
                          </TableCell>
                        );
                      }

                      // Otherwise, render cells for each child column
                      return childColumns.map((childCol) => (
                        <TableCell
                          key={`cell-${childCol.id}-${row.id}`}
                          className="px-3 py-2 text-xs border border-amber-700"
                        >
                          {cellValueMap.get(`${childCol.id}_${row.id}`) || ""}
                        </TableCell>
                      ));
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        <div className="flex justify-end mt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default TableDataViewModal;
