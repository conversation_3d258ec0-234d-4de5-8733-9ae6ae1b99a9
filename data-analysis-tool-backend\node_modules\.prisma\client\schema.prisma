// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                           Int                            @id @default(autoincrement())
  name                         String
  email                        String                         @unique
  password                     String
  projects                     Project[]
  libraryTemplates             LibraryTemplate[]
  country                      String
  city                         String?
  bio                          String?
  sector                       Sector
  organizationType             OrganizationType
  userSession                  UserSession[]
  projectUser                  ProjectUser[]
  exportFile                   ExportedFile[]
  isVerified                   Boolean                        @default(false)
  emailVerificationToken       String?
  emailVerificationExpires     DateTime?
  resetPasswordToken           String?
  resetPasswordExpires         DateTime?
  createdAt                    DateTime                       @default(now())
  updatedAt                    DateTime                       @updatedAt
  FormSubmission               FormSubmission[]
  LibraryQuestionBlockQuestion LibraryQuestionBlockQuestion[]
}

model Project {
  id               Int              @id @default(autoincrement())
  name             String
  description      String
  sector           Sector
  status           Status           @default(draft)
  country          String?
  lastSubmissionAt DateTime?
  lastDeployedAt   DateTime?
  questions        Question[]
  projectUser      ProjectUser[]
  formSubmissions  FormSubmission[]
  questionGroup    QuestionGroup[]
  exportedFile     ExportedFile[]
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  user   User @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId Int

  @@unique([name, userId])
}

model Question {
  id                 Int                 @id @default(autoincrement())
  projectId          Int
  project            Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  label              String
  inputType          InputType
  hint               String?
  placeholder        String?
  isRequired         Boolean             @default(false)
  position           Int?
  questionConditions QuestionCondition[]
  questionOptions    QuestionOption[]
  nextOptions        QuestionOption[]    @relation("NextQuestion")
  answers            Answer[]
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  questionGroup      QuestionGroup?      @relation(fields: [questionGroupId], references: [id])
  questionGroupId    Int?
  tableColumns       TableColumn[]
  tableRows          TableRow[]
}

model QuestionOption {
  id             Int       @id @default(autoincrement())
  label          String
  sublabel       String?
  code           String
  questionId     Int
  question       Question  @relation(fields: [questionId], references: [id], onDelete: Cascade)
  nextQuestionId Int?
  nextQuestion   Question? @relation("NextQuestion", fields: [nextQuestionId], references: [id])
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  Answer         Answer[]
}

model QuestionCondition {
  id         Int       @id @default(autoincrement())
  operator   Operator
  value      String
  question   Question? @relation(fields: [questionId], references: [id], onDelete: Cascade)
  questionId Int?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
}

model QuestionGroup {
  id            Int             @id @default(autoincrement())
  title         String
  order         Int             @default(autoincrement())
  parentGroupId Int?
  parentGroup   QuestionGroup?  @relation("GroupHierarchy", fields: [parentGroupId], references: [id])
  subGroups     QuestionGroup[] @relation("GroupHierarchy")
  question      Question[]
  project       Project         @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId     Int
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
}

model UserSession {
  id          Int      @id @default(autoincrement())
  deviceInfo  String
  ipAddress   String
  browserInfo String
  isActive    Boolean  @default(true)
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([userId, id])
  @@index([userId, id])
}

model LibraryTemplate {
  id               Int               @id @default(autoincrement())
  name             String
  description      String
  sector           Sector
  country          String?
  libraryQuestions LibraryQuestion[]
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt

  user                         User                           @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId                       Int
  LibraryTemplateQuestionGroup LibraryTemplateQuestionGroup[]

  @@unique([name, userId])
}

model LibraryQuestion {
  id                             Int                           @id @default(autoincrement())
  libraryTemplateId              Int
  libraryTemplate                LibraryTemplate               @relation(fields: [libraryTemplateId], references: [id], onDelete: Cascade)
  label                          String
  inputType                      InputType
  hint                           String?
  placeholder                    String?
  isRequired                     Boolean                       @default(false)
  position                       Int
  questionOptions                LibraryQuestionOption[]
  nextOptions                    LibraryQuestionOption[]       @relation("NextLibraryQuestion")
  questionConditions             LibraryQuestionCondition[]
  createdAt                      DateTime                      @default(now())
  updatedAt                      DateTime                      @updatedAt
  LibraryTemplateQuestionGroup   LibraryTemplateQuestionGroup? @relation(fields: [libraryTemplateQuestionGroupId], references: [id])
  libraryTemplateQuestionGroupId Int?
}

model LibraryQuestionOption {
  id                    Int              @id @default(autoincrement())
  label                 String
  code                  String
  libraryQuestionId     Int
  libraryQuestion       LibraryQuestion  @relation(fields: [libraryQuestionId], references: [id], onDelete: Cascade)
  nextLibraryQuestionId Int?
  nextLibraryQuestion   LibraryQuestion? @relation("NextLibraryQuestion", fields: [nextLibraryQuestionId], references: [id])
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt
}

model LibraryQuestionCondition {
  id                Int             @id @default(autoincrement())
  operator          Operator
  value             String
  libraryQuestion   LibraryQuestion @relation(fields: [libraryQuestionId], references: [id], onDelete: Cascade)
  libraryQuestionId Int
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
}

model LibraryTemplateQuestionGroup {
  id                Int                            @id @default(autoincrement())
  title             String
  order             Int                            @default(autoincrement())
  libraryTemplateId Int
  libraryTemplate   LibraryTemplate                @relation(fields: [libraryTemplateId], references: [id])
  libraryQuestions  LibraryQuestion[]
  parentGroupId     Int?
  parentGroup       LibraryTemplateQuestionGroup?  @relation("GroupHierarchy", fields: [parentGroupId], references: [id])
  subGroups         LibraryTemplateQuestionGroup[] @relation("GroupHierarchy")
  createdAt         DateTime                       @default(now())
  updatedAt         DateTime                       @updatedAt
}

model ProjectUser {
  id         Int      @id @default(autoincrement())
  permission Json     @default("{}")
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId     Int
  project    Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId  Int
  createdAt  DateTime @default(now())
  updatedAT  DateTime @updatedAt

  @@unique([userId, projectId], name: "userId_projectId")
}

model FormSubmission {
  id              Int       @id @default(autoincrement())
  projectId       Int?
  project         Project?  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  userId          Int?
  user            User?     @relation(fields: [userId], references: [id])
  submittedAt     DateTime?
  startedAt       DateTime  @default(now())
  completedAt     DateTime?
  durationSeconds Int?
  status          String    @default("draft")
  deviceInfo      String?
  location        String?
  answers         Answer[]
  loginRequired   Boolean   @default(true)
  metadata        Json?     @default("{}")
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

model Answer {
  id               Int             @id @default(autoincrement())
  formSubmissionId Int?
  formSubmission   FormSubmission? @relation(fields: [formSubmissionId], references: [id])
  questionId       Int?
  question         Question?       @relation(fields: [questionId], references: [id], onDelete: Cascade)
  value            String
  answerType       String?
  imageUrl         String?
  questionOption   QuestionOption? @relation(fields: [questionOptionId], references: [id])
  questionOptionId Int?
  isOtherOption    Boolean         @default(false)
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
}

model ExportedFile {
  id          Int      @id @default(autoincrement())
  projectId   Int?
  project     Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user        User?    @relation(fields: [userId], references: [id])
  userId      Int?
  fileName    String
  fileType    String
  contentType String
  fileBuffer  Bytes
  createdAt   DateTime @default(now())
}

// model LibraryQuestionBlockTemplate {
//   id                           Int                            @id @default(autoincrement())
//   name                         String
//   description                  String
//   sector                       Sector
//   country                      String?
//   libraryQuestionBlockQuestion LibraryQuestionBlockQuestion[]
//   createdAt                    DateTime                       @default(now())
//   updatedAt                    DateTime                       @updatedAt

//   user                              User                                @relation(fields: [userId], references: [id], onDelete: Cascade)
//   userId                            Int
//   LibraryQuestionBlockQuestionGroup LibraryQuestionBlockQuestionGroup[]

//   @@unique([name, userId])
// }

model LibraryQuestionBlockQuestion {
  id                                  Int                                     @id @default(autoincrement())
  label                               String
  inputType                           InputType
  hint                                String?
  placeholder                         String?
  isRequired                          Boolean                                 @default(false)
  position                            Int
  user                                User                                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId                              Int
  questionOptions                     LibraryQuestionBlockQuestionOption[]
  nextOptions                         LibraryQuestionBlockQuestionOption[]    @relation("NextLibraryQuestionBlock")
  questionConditions                  LibraryQuestionBlockQuestionCondition[]
  createdAt                           DateTime                                @default(now())
  updatedAt                           DateTime                                @updatedAt
  LibraryQuestionBlockQuestionGroup   LibraryQuestionBlockQuestionGroup?      @relation(fields: [libraryQuestionBlockQuestionGroupId], references: [id])
  libraryQuestionBlockQuestionGroupId Int?
}

model LibraryQuestionBlockQuestionOption {
  id                             Int                           @id @default(autoincrement())
  label                          String
  code                           String
  nextLibraryQuestionId          Int?
  createdAt                      DateTime                      @default(now())
  updatedAt                      DateTime                      @updatedAt
  libraryQuestionBlockQuestion   LibraryQuestionBlockQuestion  @relation(fields: [libraryQuestionBlockQuestionId], references: [id], onDelete: Cascade)
  libraryQuestionBlockQuestionId Int
  nextLibraryQuestion            LibraryQuestionBlockQuestion? @relation("NextLibraryQuestionBlock", fields: [nextLibraryQuestionId], references: [id])
}

model LibraryQuestionBlockQuestionCondition {
  id                             Int                          @id @default(autoincrement())
  operator                       Operator
  value                          String
  createdAt                      DateTime                     @default(now())
  updatedAt                      DateTime                     @updatedAt
  libraryQuestionBlockQuestion   LibraryQuestionBlockQuestion @relation(fields: [libraryQuestionBlockQuestionId], references: [id], onDelete: Cascade)
  libraryQuestionBlockQuestionId Int
}

model LibraryQuestionBlockQuestionGroup {
  id                    Int                                 @id @default(autoincrement())
  title                 String
  order                 Int                                 @default(autoincrement())
  questionBlockQuestion LibraryQuestionBlockQuestion[]
  parentGroupId         Int?
  parentGroup           LibraryQuestionBlockQuestionGroup?  @relation("GroupHierarchy", fields: [parentGroupId], references: [id])
  subGroups             LibraryQuestionBlockQuestionGroup[] @relation("GroupHierarchy")
  createdAt             DateTime                            @default(now())
  updatedAt             DateTime                            @updatedAt
  // LibraryQuestionBlockTemplate   LibraryQuestionBlockTemplate        @relation(fields: [libraryQuestionBlockTemplateId], references: [id])
  // libraryQuestionBlockTemplateId Int
}

enum Status {
  deployed
  archived
  draft
}

enum InputType {
  selectone
  selectmany
  text
  number
  decimal
  date
  dateandtime
  table
}

enum Operator {
  is_less_than
  is_greater_than
  is_equal_to
  less_than_or_equal_to
  greater_than_or_equal_to
}

enum Sector {
  information_media
  econommic_social_development
  security_police_peacekeeping
  disarmament_and_demobilization
  environment
  private_sector
  humanitarian_coordination_information_management
  humanitarian_multiple_clusters
  humanitarian_camp_management_and_coordination
  humanitarian_early_recovery
  humanitarian_education
  humanitarian_emergency_shelter
  humanitarian_emergency_telecoms
  humanitarian_food_security
  humanitarian_health
  humanitarian_logistics
  humanitarian_nutrition
  humanitarian_protection
  humanitarian_sanitation_water_and_hygiene
  other
}

enum OrganizationType {
  i_am_not_associated_with_any_organization
  non_profit_organization
  government_institution
  educational_organization
  a_commercial_or_for_profit_company
}

model TableColumn {
  id             Int           @id @default(autoincrement())
  questionId     Int           @map("question_id")
  columnName     String        @map("column_name")
  parentColumnId Int?          @map("parent_column_id")
  createdAt      DateTime      @default(now()) @map("created_at")
  updatedAt      DateTime      @updatedAt @map("updated_at")
  question       Question      @relation(fields: [questionId], references: [id], onDelete: Cascade)
  parentColumn   TableColumn?  @relation("ColumnHierarchy", fields: [parentColumnId], references: [id])
  childColumns   TableColumn[] @relation("ColumnHierarchy")
  columnRows     ColumnRow[]

  @@map("columns")
}

model TableRow {
  id         Int         @id @default(autoincrement())
  rowsName   String      @map("rows_name")
  questionId Int         @map("question_id")
  createdAt  DateTime    @default(now()) @map("created_at")
  updatedAt  DateTime    @updatedAt @map("updated_at")
  question   Question    @relation(fields: [questionId], references: [id], onDelete: Cascade)
  columnRows ColumnRow[]

  @@map("rows")
}

model ColumnRow {
  id        Int         @id @default(autoincrement())
  columnId  Int         @map("column_id")
  rowsId    Int         @map("rows_id")
  value     String?
  code      String?
  createdAt DateTime    @default(now()) @map("created_at")
  updatedAt DateTime    @updatedAt @map("updated_at")
  column    TableColumn @relation(fields: [columnId], references: [id], onDelete: Cascade)
  row       TableRow    @relation(fields: [rowsId], references: [id], onDelete: Cascade)

  @@unique([columnId, rowsId])
  @@map("column_rows")
}
